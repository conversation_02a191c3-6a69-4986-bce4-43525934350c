'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { ApiService } from '@/lib/api';
import { StockNotificationRecord, ProductGroup, StockNotificationFilters } from '@/lib/types';

export default function StockNotificationRecordsPage() {
  const [records, setRecords] = useState<StockNotificationRecord[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRecord, setSelectedRecord] = useState<StockNotificationRecord | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [resolveNotes, setResolveNotes] = useState('');

  // Filter states
  const [filters, setFilters] = useState<StockNotificationFilters>({
    dateFrom: '',
    dateTo: '',
    productGroupId: undefined,
    notificationLevel: undefined,
    isRead: undefined,
    isResolved: undefined
  });
  const [searchProduct, setSearchProduct] = useState<string>('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load product groups
      const groupsResponse = await ApiService.fetchProductGroups();
      let groupsData: ProductGroup[] = [];
      if (groupsResponse.result) {
        if (Array.isArray(groupsResponse.result)) {
          groupsData = groupsResponse.result;
        } else if (typeof groupsResponse.result === 'object') {
          const result = groupsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            groupsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            groupsData = result.items;
          }
        }
      }
      setProductGroups(groupsData);

      // Load notification records (mock data for now)
      loadNotificationRecords();
      
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadNotificationRecords = () => {
    // Mock data - 在实际应用中应该从API加载
    const mockRecords: StockNotificationRecord[] = [
      {
        id: 1,
        ruleId: 1,
        productId: 1,
        productName: '示例产品 1',
        productBarcode: 'TEST001',
        currentStock: 5,
        thresholdQuantity: 10,
        notificationLevel: 'low',
        message: '产品"示例产品 1"库存不足，当前库存：5，警戒线：10',
        isRead: false,
        isResolved: false,
        notificationMethod: 'dashboard',
        sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        ruleId: 2,
        productId: 2,
        productName: '示例产品 2',
        productBarcode: 'TEST002',
        currentStock: 2,
        thresholdQuantity: 15,
        notificationLevel: 'critical',
        message: '产品"示例产品 2"库存严重不足，当前库存：2，警戒线：15',
        isRead: true,
        isResolved: false,
        notificationMethod: 'both',
        recipients: ['<EMAIL>'],
        sentAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        readAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        ruleId: 3,
        productId: 3,
        productName: '示例产品 3',
        productBarcode: 'TEST003',
        currentStock: 0,
        thresholdQuantity: 20,
        notificationLevel: 'out_of_stock',
        message: '产品"示例产品 3"已缺货，当前库存：0',
        isRead: true,
        isResolved: true,
        notificationMethod: 'email',
        recipients: ['<EMAIL>', '<EMAIL>'],
        sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        readAt: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString(), // 20 hours ago
        resolvedAt: new Date(Date.now() - 18 * 60 * 60 * 1000).toISOString(), // 18 hours ago
        resolvedBy: 1,
        resolvedByName: 'Admin',
        notes: '已补充库存，问题解决',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ];
    setRecords(mockRecords);
  };

  const handleViewDetails = (record: StockNotificationRecord) => {
    setSelectedRecord(record);
    setResolveNotes('');
    setIsDetailModalOpen(true);
    
    // Mark as read if not already read
    if (!record.isRead) {
      markAsRead(record.id!);
    }
  };

  const markAsRead = (recordId: number) => {
    setRecords(prev => prev.map(r => 
      r.id === recordId ? { 
        ...r, 
        isRead: true, 
        readAt: new Date().toISOString() 
      } : r
    ));
  };

  const markAsResolved = async (recordId: number, notes: string) => {
    setRecords(prev => prev.map(r => 
      r.id === recordId ? { 
        ...r, 
        isResolved: true, 
        resolvedAt: new Date().toISOString(),
        resolvedBy: 1, // Mock user ID
        resolvedByName: 'Admin',
        notes: notes
      } : r
    ));
    setIsDetailModalOpen(false);
    setSelectedRecord(null);
    setResolveNotes('');
  };

  const handleResolve = () => {
    if (selectedRecord) {
      markAsResolved(selectedRecord.id!, resolveNotes);
    }
  };

  const getNotificationLevelBadge = (level: string) => {
    const styles = {
      low: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      critical: 'bg-red-100 text-red-800 border-red-200',
      out_of_stock: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    const labels = {
      low: '库存偏低',
      critical: '库存紧急',
      out_of_stock: '缺货'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${styles[level as keyof typeof styles]}`}>
        {labels[level as keyof typeof labels]}
      </span>
    );
  };

  const getStatusBadge = (record: StockNotificationRecord) => {
    if (record.isResolved) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">已解决</span>;
    }
    if (record.isRead) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">已读</span>;
    }
    return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200">未读</span>;
  };

  // Apply filters
  const filteredRecords = records.filter(record => {
    // Date filter
    if (filters.dateFrom && new Date(record.sentAt) < new Date(filters.dateFrom)) {
      return false;
    }
    if (filters.dateTo && new Date(record.sentAt) > new Date(filters.dateTo + 'T23:59:59')) {
      return false;
    }
    
    // Product group filter (would need product group info)
    // This is a simplification - in real app, you'd need to join product data
    
    // Notification level filter
    if (filters.notificationLevel && record.notificationLevel !== filters.notificationLevel) {
      return false;
    }
    
    // Read status filter
    if (filters.isRead !== undefined && record.isRead !== filters.isRead) {
      return false;
    }
    
    // Resolved status filter
    if (filters.isResolved !== undefined && record.isResolved !== filters.isResolved) {
      return false;
    }
    
    // Product search
    if (searchProduct && !record.productName.toLowerCase().includes(searchProduct.toLowerCase())) {
      return false;
    }
    
    return true;
  });

  // Statistics
  const stats = {
    total: records.length,
    unread: records.filter(r => !r.isRead).length,
    unresolved: records.filter(r => !r.isResolved).length,
    critical: records.filter(r => r.notificationLevel === 'critical' && !r.isResolved).length
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">📝</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">库存通知记录</h1>
                <p className="text-gray-600 mt-1">查看和管理所有库存通知记录</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/stock-notifications" className="text-orange-600 hover:text-orange-800 text-sm font-medium">
                🔔 通知设置
              </Link>
              <Link href="/products" className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                ← 返回产品页面
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总通知数</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12z" />
              </svg>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">未读通知</p>
              <p className="text-2xl font-bold text-orange-600">{stats.unread}</p>
            </div>
            <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
              <svg className="h-4 w-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">未解决</p>
              <p className="text-2xl font-bold text-red-600">{stats.unresolved}</p>
            </div>
            <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">紧急通知</p>
              <p className="text-2xl font-bold text-red-800">{stats.critical}</p>
            </div>
            <div className="h-8 w-8 bg-red-200 rounded-full flex items-center justify-center">
              <svg className="h-4 w-4 text-red-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">通知级别</label>
            <select
              value={filters.notificationLevel || ''}
              onChange={(e) => setFilters(prev => ({ 
                ...prev, 
                notificationLevel: e.target.value ? e.target.value as any : undefined 
              }))}
              className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">全部级别</option>
              <option value="low">库存偏低</option>
              <option value="critical">库存紧急</option>
              <option value="out_of_stock">缺货</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <select
              value={filters.isResolved === undefined ? '' : (filters.isResolved ? 'resolved' : 'unresolved')}
              onChange={(e) => setFilters(prev => ({ 
                ...prev, 
                isResolved: e.target.value === '' ? undefined : e.target.value === 'resolved'
              }))}
              className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">全部状态</option>
              <option value="unresolved">未解决</option>
              <option value="resolved">已解决</option>
            </select>
          </div>
        </div>
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">搜索产品</label>
          <Input
            value={searchProduct}
            onChange={(e) => setSearchProduct(e.target.value)}
            placeholder="按产品名称搜索..."
            className="max-w-md"
          />
        </div>
      </div>

      {/* Records Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存情况</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知时间</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRecords.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                    暂无通知记录
                  </td>
                </tr>
              ) : (
                filteredRecords.map((record) => (
                  <tr key={record.id} className={`hover:bg-gray-50 ${!record.isRead ? 'bg-blue-50' : ''}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(record)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getNotificationLevelBadge(record.notificationLevel)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{record.productName}</div>
                      {record.productBarcode && (
                        <div className="text-sm text-gray-500">{record.productBarcode}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        当前: <span className="font-semibold">{record.currentStock}</span>
                      </div>
                      <div className="text-sm text-gray-500">
                        阈值: {record.thresholdQuantity}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(record.sentAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Button
                        onClick={() => handleViewDetails(record)}
                        variant="outline"
                        size="sm"
                      >
                        查看详情
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Detail Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>通知详情</DialogTitle>
          </DialogHeader>
          
          {selectedRecord && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">产品名称</label>
                  <p className="text-sm text-gray-900">{selectedRecord.productName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">产品条码</label>
                  <p className="text-sm text-gray-900">{selectedRecord.productBarcode || '-'}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">当前库存</label>
                  <p className="text-sm font-semibold text-red-600">{selectedRecord.currentStock}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">阈值</label>
                  <p className="text-sm text-gray-900">{selectedRecord.thresholdQuantity}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">通知级别</label>
                  <div>{getNotificationLevelBadge(selectedRecord.notificationLevel)}</div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">通知消息</label>
                <div className="bg-gray-50 rounded-lg p-3 text-sm text-gray-900">
                  {selectedRecord.message}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">通知方式</label>
                  <p className="text-sm text-gray-900">
                    {selectedRecord.notificationMethod === 'email' && '邮件通知'}
                    {selectedRecord.notificationMethod === 'dashboard' && '仪表板通知'}
                    {selectedRecord.notificationMethod === 'both' && '邮件+仪表板通知'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">发送时间</label>
                  <p className="text-sm text-gray-900">
                    {new Date(selectedRecord.sentAt).toLocaleString()}
                  </p>
                </div>
              </div>

              {selectedRecord.recipients && selectedRecord.recipients.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">邮件接收者</label>
                  <p className="text-sm text-gray-900">{selectedRecord.recipients.join(', ')}</p>
                </div>
              )}

              {selectedRecord.isResolved && (
                <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                  <div className="grid grid-cols-2 gap-4 mb-2">
                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">解决时间</label>
                      <p className="text-sm text-green-900">
                        {selectedRecord.resolvedAt ? new Date(selectedRecord.resolvedAt).toLocaleString() : '-'}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">解决人员</label>
                      <p className="text-sm text-green-900">{selectedRecord.resolvedByName || '-'}</p>
                    </div>
                  </div>
                  {selectedRecord.notes && (
                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">解决备注</label>
                      <p className="text-sm text-green-900">{selectedRecord.notes}</p>
                    </div>
                  )}
                </div>
              )}

              {!selectedRecord.isResolved && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">解决备注</label>
                  <Textarea
                    value={resolveNotes}
                    onChange={(e) => setResolveNotes(e.target.value)}
                    placeholder="请输入解决方案或备注..."
                    rows={3}
                  />
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailModalOpen(false)}>
              关闭
            </Button>
            {selectedRecord && !selectedRecord.isResolved && (
              <Button onClick={handleResolve} className="bg-green-600 hover:bg-green-700">
                标记为已解决
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </div>
  );
}