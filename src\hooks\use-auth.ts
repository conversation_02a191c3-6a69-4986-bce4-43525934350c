'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ApiService } from '@/lib/api';
import { LoginCredentials, AuthToken } from '@/lib/types';

interface AuthState {
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await ApiService.login(credentials);
          const token = response.result?.token;
          
          if (token) {
            const fullToken = `Bearer ${token}`;
            if (typeof window !== 'undefined') {
              localStorage.setItem('token', fullToken);
            }
            set({
              token: fullToken,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            set({
              isLoading: false,
              error: '登录失败：无有效 token'
            });
          }
        } catch (error) {
          let errorMessage = '登录失败';
          
          if (error instanceof Error) {
            if (error.message.includes('401')) {
              errorMessage = '用户名或密码错误';
            } else if (error.message.includes('403')) {
              errorMessage = '账户被禁用，请联系管理员';
            } else {
              errorMessage = `网络错误：${error.message}`;
            }
          }
          
          set({
            isLoading: false,
            error: errorMessage
          });
        }
      },

      logout: () => {
        if (typeof window !== 'undefined') {
          localStorage.removeItem('token');
          // 重定向到登录页面
          window.location.href = '/login';
        }
        set({
          token: null,
          isAuthenticated: false,
          error: null
        });
      },

      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ token: state.token, isAuthenticated: state.isAuthenticated }),
      // 添加水合处理
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 确保客户端状态与服务器端一致
          if (typeof window !== 'undefined') {
            const token = localStorage.getItem('token');
            if (token) {
              state.token = token;
              state.isAuthenticated = true;
            }
          }
        }
      }
    }
  )
); 