'use client';

import { useState, useEffect } from 'react';
import { ApiService } from '@/lib/api';
import { Product, ProductGroup, InventoryCountConfigProduct } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { LoadingIndicator } from '@/components/ui/loading';

interface ProductGroupSelectorProps {
  selectedProducts: InventoryCountConfigProduct[];
  onProductsChange: (products: InventoryCountConfigProduct[]) => void;
}

export function ProductGroupSelector({ selectedProducts, onProductsChange }: ProductGroupSelectorProps) {
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [productGroupsResponse, productsResponse] = await Promise.all([
        ApiService.fetchProductGroups(),
        ApiService.fetchAllProducts(),
      ]);

      // 处理产品组数据
      if (productGroupsResponse.result) {
        let productGroupsData = [];
        if (Array.isArray(productGroupsResponse.result)) {
          productGroupsData = productGroupsResponse.result;
        } else if (productGroupsResponse.result && typeof productGroupsResponse.result === 'object') {
          const result = productGroupsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            productGroupsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productGroupsData = result.items;
          }
        }
        setProductGroups(productGroupsData);
      }

      // 处理产品数据
      if (productsResponse.result) {
        let productsData = [];
        if (Array.isArray(productsResponse.result)) {
          productsData = productsResponse.result;
        } else if (productsResponse.result && typeof productsResponse.result === 'object') {
          const result = productsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            productsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productsData = result.items;
          }
        }
        setProducts(productsData);
      }
    } catch (err) {
      console.error('加载产品数据失败:', err);
      setError(err instanceof Error ? err.message : '加载产品数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleGroupExpansion = (groupId: number) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const getProductsInGroup = (groupId: number) => {
    return products.filter(product => product.productGroupId === groupId);
  };

  const isProductSelected = (productId: number) => {
    return selectedProducts.some(p => p.productId === productId);
  };

  const isGroupFullySelected = (groupId: number) => {
    const groupProducts = getProductsInGroup(groupId);
    return groupProducts.length > 0 && groupProducts.every(product => isProductSelected(product.id));
  };

  const isGroupPartiallySelected = (groupId: number) => {
    const groupProducts = getProductsInGroup(groupId);
    return groupProducts.some(product => isProductSelected(product.id)) && !isGroupFullySelected(groupId);
  };

  const toggleProduct = (product: Product) => {
    const isSelected = isProductSelected(product.id);
    const productGroup = productGroups.find(g => g.id === product.productGroupId);
    
    if (isSelected) {
      // 移除产品
      onProductsChange(selectedProducts.filter(p => p.productId !== product.id));
    } else {
      // 添加产品
      const newProduct: InventoryCountConfigProduct = {
        configId: 0, // 将在保存时设置
        productId: product.id,
        productName: product.name,
        productGroupId: product.productGroupId,
        productGroupName: productGroup?.name || '未分组',
        productBarcode: product.barcode,
        isSelected: true,
      };
      onProductsChange([...selectedProducts, newProduct]);
    }
  };

  const toggleGroup = (groupId: number) => {
    const groupProducts = getProductsInGroup(groupId);
    const isFullySelected = isGroupFullySelected(groupId);
    const productGroup = productGroups.find(g => g.id === groupId);

    if (isFullySelected) {
      // 移除整个组的所有产品
      onProductsChange(selectedProducts.filter(p => p.productGroupId !== groupId));
    } else {
      // 添加整个组的所有产品
      const newProducts = groupProducts
        .filter(product => !isProductSelected(product.id))
        .map(product => ({
          configId: 0,
          productId: product.id,
          productName: product.name,
          productGroupId: product.productGroupId,
          productGroupName: productGroup?.name || '未分组',
          productBarcode: product.barcode,
          isSelected: true,
        }));
      
      onProductsChange([...selectedProducts, ...newProducts]);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingIndicator text="加载产品数据中..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="text-red-600 text-xl mr-3">❌</div>
          <div>
            <h3 className="text-red-800 font-medium">加载失败</h3>
            <p className="text-red-700 mt-1">{error}</p>
            <Button onClick={loadData} variant="outline" size="sm" className="mt-2">
              重试
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">选择盘点产品</h3>
        <div className="text-sm text-gray-600">
          已选择 {selectedProducts.length} 个产品
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg max-h-96 overflow-y-auto">
        {productGroups.map(group => {
          const groupProducts = getProductsInGroup(group.id);
          const isExpanded = expandedGroups.has(group.id);
          const isFullySelected = isGroupFullySelected(group.id);
          const isPartiallySelected = isGroupPartiallySelected(group.id);

          return (
            <div key={group.id} className="border-b border-gray-200 last:border-b-0">
              {/* 产品组标题 */}
              <div className="flex items-center p-4 bg-gray-50 hover:bg-gray-100">
                <input
                  type="checkbox"
                  checked={isFullySelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={() => toggleGroup(group.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3"
                />
                <button
                  onClick={() => toggleGroupExpansion(group.id)}
                  className="flex-1 flex items-center justify-between text-left"
                >
                  <div>
                    <span className="font-medium text-gray-900">{group.name}</span>
                    <span className="ml-2 text-sm text-gray-500">
                      ({groupProducts.length} 个产品)
                    </span>
                  </div>
                  <div className="text-gray-400">
                    {isExpanded ? '▼' : '▶'}
                  </div>
                </button>
              </div>

              {/* 产品列表 */}
              {isExpanded && (
                <div className="bg-white">
                  {groupProducts.length === 0 ? (
                    <div className="p-4 text-gray-500 text-center">
                      该产品组下没有产品
                    </div>
                  ) : (
                    groupProducts.map(product => (
                      <div
                        key={product.id}
                        className="flex items-center p-3 pl-8 hover:bg-gray-50 border-t border-gray-100"
                      >
                        <input
                          type="checkbox"
                          checked={isProductSelected(product.id)}
                          onChange={() => toggleProduct(product)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{product.name}</div>
                          {product.barcode && (
                            <div className="text-sm text-gray-500">条码: {product.barcode}</div>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {product.id}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {selectedProducts.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">已选择的产品预览</h4>
          <div className="max-h-32 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {selectedProducts.map(product => (
                <div key={product.productId} className="text-sm text-blue-800">
                  • {product.productName} ({product.productGroupName})
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
