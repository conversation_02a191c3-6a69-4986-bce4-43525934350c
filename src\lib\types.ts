// API 响应类型
export interface ApiResponse<T = unknown> {
  result?: T;
  success?: boolean;
  message?: string;
  errorCodeReadable?: string;
}

// 认证相关类型
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthToken {
  token: string;
  expiresAt?: string;
}

// 产品相关类型
export interface Product {
  id: number;
  name: string;
  description?: string;
  barcode?: string;
  price: number;
  cost?: number;
  productGroupId?: number;
  productGroupName?: string;
  images?: ProductImage[];
  userPrices?: ProductUserPrice[];
  disallowedUserGroups?: DisallowedUserGroup[];
  purchaseAvailability?: PurchaseAvailability;
  stock?: number | string;
  isDeleted?: boolean;
  deleted?: boolean;
  status?: string;
  // 新增字段用于完整的产品编辑
  productType?: number;
  disallowClientOrder?: boolean;
  restrictGuestSale?: boolean;
  restrictSale?: boolean;
  purchaseOptions?: number;
  points?: number;
  pointsPrice?: number;
  enableStock?: boolean;
  disallowSaleIfOutOfStock?: boolean;
  stockAlert?: boolean;
  stockAlertThreshold?: number;
  stockTargetDifferentProduct?: boolean;
  stockTargetProductId?: number;
  stockProductAmount?: number;
  isService?: boolean;
  displayOrder?: number;
}

export interface ProductImage {
  id: number;
  image: string;
  isPrimary: boolean;
}

export interface ProductUserPrice {
  id: number;
  productId?: number;
  userGroupId: number;
  userGroupName: string;
  price: number;
  pointsPrice?: number;
  purchaseOptions?: number;
  isEnabled?: boolean;
}

export interface DisallowedUserGroup {
  id: number;
  productId?: number;
  userGroupId: number;
  userGroupName?: string;
  isDisallowed?: boolean;
}

export interface PurchaseAvailability {
  id: number;
  isAvailable: boolean;
  availableFrom?: string;
  availableTo?: string;
}

// 产品组类型
export interface ProductGroup {
  id: number;
  name: string;
  description?: string;
}

// 用户组类型
export interface UserGroup {
  id: number;
  name: string;
  description?: string;
}

// 库存相关类型
export interface StockInfo {
  onHand: number;
  isDisabled?: boolean;
}

export interface StockUpdate {
  id: number;
  amount: number;
  type?: 0 | 1; // 0: 增加, 1: 减少
}

// 筛选和排序类型
export interface ProductFilters {
  search?: string;
  productGroupId?: number;
  inStockOnly?: boolean;
  sortBy?: string;
}

// 分页类型
export interface Pagination {
  limit: number;
  offset?: number;
  total?: number;
}

// 操作员类型
export interface Operator {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  isEnabled: boolean;
  roles: string[];
  permissions: string[];
}

// 库存盘点相关类型
export interface InventoryCountConfig {
  id?: number;
  configName: string;
  authorizedOperators: number[];
  frequency: 'daily' | 'weekly' | 'monthly';
  scheduleTime: string; // HH:mm format
  scheduleDays?: number[]; // 0-6 for weekly, 1-31 for monthly
  isActive: boolean;
  createdBy: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface InventoryCountConfigProduct {
  id?: number;
  configId: number;
  productId: number;
  productName: string;
  productGroupId?: number;
  productGroupName?: string;
  productBarcode?: string;
  isSelected: boolean;
  createdAt?: string;
}

export interface InventoryCountRecord {
  id?: number;
  sessionId: string;
  productId: number;
  productName: string;
  productBarcode?: string;
  productGroupId?: number;
  productGroupName?: string;
  systemStock: number;
  countedStock: number | null;
  difference: number;
  unitPrice?: number;
  differenceValue?: number;
  operatorId: number;
  operatorName: string;
  status: 'pending' | 'counted' | 'submitted';
  countDate: string;
  countTime: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 保持向后兼容
export interface InventoryCountItem extends InventoryCountRecord {
  countSessionId: string; // 映射到 sessionId
}

export interface InventoryCountSession {
  id: string;
  configId?: number;
  operatorId: number;
  operatorName: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  startTime: string;
  endTime?: string;
  totalItems: number;
  completedItems: number;
  discrepancyItems: number;
  totalDiscrepancyValue?: number;
  notes?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface InventoryCountSummary {
  id?: number;
  sessionId: string;
  summaryDate: string;
  operatorId: number;
  operatorName: string;
  totalProducts: number;
  countedProducts: number;
  productsWithDiscrepancy: number;
  totalSystemStock: number;
  totalCountedStock: number;
  totalDifference: number;
  totalDifferenceValue?: number;
  accuracyPercentage: number;
  createdAt?: string;
}

export interface InventoryCountFilters {
  dateFrom?: string;
  dateTo?: string;
  productGroupId?: number;
  operatorId?: number;
  status?: 'draft' | 'submitted';
  hasDiscrepancy?: boolean;
}

// 库存通知相关类型
export interface StockNotificationRule {
  id?: number;
  productId: number;
  productName: string;
  productBarcode?: string;
  productGroupId?: number;
  productGroupName?: string;
  thresholdQuantity: number;
  isEnabled: boolean;
  notificationMethod: 'email' | 'dashboard' | 'both';
  recipients?: string[]; // email addresses
  createdBy: number;
  createdByName: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface StockNotificationRecord {
  id?: number;
  ruleId: number;
  productId: number;
  productName: string;
  productBarcode?: string;
  currentStock: number;
  thresholdQuantity: number;
  notificationLevel: 'low' | 'critical' | 'out_of_stock';
  message: string;
  isRead: boolean;
  isResolved: boolean;
  notificationMethod: 'email' | 'dashboard' | 'both';
  recipients?: string[];
  sentAt: string;
  readAt?: string;
  resolvedAt?: string;
  resolvedBy?: number;
  resolvedByName?: string;
  notes?: string;
  createdAt?: string;
}

export interface StockNotificationSettings {
  emailEnabled: boolean;
  dashboardEnabled: boolean;
  defaultRecipients: string[];
  checkInterval: number; // minutes
  criticalThreshold: number; // percentage below threshold for critical alert
  isActive: boolean;
  updatedBy: number;
  updatedByName: string;
  updatedAt?: string;
}

export interface StockNotificationFilters {
  dateFrom?: string;
  dateTo?: string;
  productGroupId?: number;
  productId?: number;
  notificationLevel?: 'low' | 'critical' | 'out_of_stock';
  isRead?: boolean;
  isResolved?: boolean;
}

// BoxHero API 相关类型
export interface BoxHeroItem {
  id: string;
  name: string;
  barcode?: string;
  quantity: number;
  unit?: string;
  location?: string;
  description?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface BoxHeroApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface BoxHeroInventoryCache {
  [barcode: string]: {
    quantity: number;
    timestamp: number;
    itemId: string;
  };
}