'use client';

import { useState } from 'react';
import { StockUpdate } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface StockControlProps {
  productId: number;
  currentStock: string | number;
  onUpdate: (update: StockUpdate) => Promise<void>;
  isLoading: boolean;
}

export function StockControl({ productId, currentStock, onUpdate, isLoading }: StockControlProps) {
  const [amount, setAmount] = useState('');
  const [isSettingStock, setIsSettingStock] = useState(false);

  const handleAddStock = async () => {
    if (!amount || isNaN(Number(amount))) return;
    
    await onUpdate({
      id: productId,
      amount: Number(amount),
      type: 0
    });
    setAmount('');
  };

  const handleRemoveStock = async () => {
    if (!amount || isNaN(Number(amount))) return;
    
    await onUpdate({
      id: productId,
      amount: Number(amount),
      type: 1
    });
    setAmount('');
  };

  const handleSetStock = async () => {
    if (!amount || isNaN(Number(amount))) return;
    
    setIsSettingStock(true);
    try {
      await onUpdate({
        id: productId,
        amount: Number(amount)
      });
      setAmount('');
    } finally {
      setIsSettingStock(false);
    }
  };

  const isDisabled = isLoading || !amount || isNaN(Number(amount)) || Number(amount) <= 0;

  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <Input
          type="number"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="数量"
          className="flex-1"
          disabled={isLoading}
          min="1"
        />
      </div>

      <div className="grid grid-cols-3 gap-2">
        <Button
          onClick={handleAddStock}
          disabled={isDisabled}
          variant="primary"
          size="sm"
          className="bg-green-600 hover:bg-green-700"
        >
          增加
        </Button>

        <Button
          onClick={handleRemoveStock}
          disabled={isDisabled}
          variant="danger"
          size="sm"
        >
          减少
        </Button>

        <Button
          onClick={handleSetStock}
          disabled={isDisabled || isSettingStock}
          variant="primary"
          size="sm"
        >
          {isSettingStock ? '设置中...' : '设置'}
        </Button>
      </div>
    </div>
  );
} 