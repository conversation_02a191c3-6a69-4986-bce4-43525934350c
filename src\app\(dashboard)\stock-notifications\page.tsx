'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { ApiService } from '@/lib/api';
import { StockNotificationRule, Product, ProductGroup } from '@/lib/types';

export default function StockNotificationsPage() {
  const [rules, setRules] = useState<StockNotificationRule[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<StockNotificationRule | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  // Form states
  const [selectedProductId, setSelectedProductId] = useState<number>(0);
  const [thresholdQuantity, setThresholdQuantity] = useState<number>(10);
  const [notificationMethod, setNotificationMethod] = useState<'email' | 'dashboard' | 'both'>('dashboard');
  const [recipients, setRecipients] = useState<string>('');
  const [isEnabled, setIsEnabled] = useState<boolean>(true);

  // Filter states
  const [filterProductGroup, setFilterProductGroup] = useState<number>(0);
  const [searchProduct, setSearchProduct] = useState<string>('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load products and product groups
      const [productsResponse, groupsResponse] = await Promise.all([
        ApiService.fetchAllProducts(),
        ApiService.fetchProductGroups()
      ]);

      // Process products data
      let productsData: Product[] = [];
      if (productsResponse.result) {
        if (Array.isArray(productsResponse.result)) {
          productsData = productsResponse.result;
        } else if (typeof productsResponse.result === 'object') {
          const result = productsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            productsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productsData = result.items;
          }
        }
      }
      setProducts(productsData);

      // Process product groups data
      let groupsData: ProductGroup[] = [];
      if (groupsResponse.result) {
        if (Array.isArray(groupsResponse.result)) {
          groupsData = groupsResponse.result;
        } else if (typeof groupsResponse.result === 'object') {
          const result = groupsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            groupsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            groupsData = result.items;
          }
        }
      }
      setProductGroups(groupsData);

      // Load existing notification rules (mock data for now)
      loadNotificationRules();
      
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadNotificationRules = () => {
    // Mock data - 在实际应用中应该从API加载
    const mockRules: StockNotificationRule[] = [
      {
        id: 1,
        productId: 1,
        productName: '示例产品 1',
        productBarcode: 'TEST001',
        thresholdQuantity: 10,
        isEnabled: true,
        notificationMethod: 'dashboard',
        createdBy: 1,
        createdByName: 'Admin',
        createdAt: new Date().toISOString()
      }
    ];
    setRules(mockRules);
  };

  const resetForm = () => {
    setSelectedProductId(0);
    setThresholdQuantity(10);
    setNotificationMethod('dashboard');
    setRecipients('');
    setIsEnabled(true);
    setEditingRule(null);
  };

  const handleAddRule = () => {
    resetForm();
    setIsAddModalOpen(true);
  };

  const handleEditRule = (rule: StockNotificationRule) => {
    setEditingRule(rule);
    setSelectedProductId(rule.productId);
    setThresholdQuantity(rule.thresholdQuantity);
    setNotificationMethod(rule.notificationMethod);
    setRecipients(rule.recipients?.join(', ') || '');
    setIsEnabled(rule.isEnabled);
    setIsEditModalOpen(true);
  };

  const handleSaveRule = async () => {
    if (!selectedProductId) {
      alert('请选择产品');
      return;
    }

    const selectedProduct = products.find(p => p.id === selectedProductId);
    if (!selectedProduct) {
      alert('找不到选择的产品');
      return;
    }

    const newRule: StockNotificationRule = {
      id: editingRule?.id || Date.now(), // Mock ID
      productId: selectedProductId,
      productName: selectedProduct.name,
      productBarcode: selectedProduct.barcode,
      productGroupId: selectedProduct.productGroupId,
      productGroupName: selectedProduct.productGroupName,
      thresholdQuantity,
      isEnabled,
      notificationMethod,
      recipients: recipients.split(',').map(r => r.trim()).filter(r => r),
      createdBy: 1, // Mock user ID
      createdByName: 'Admin',
      createdAt: editingRule?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Update or add rule
    if (editingRule) {
      setRules(prev => prev.map(r => r.id === editingRule.id ? newRule : r));
    } else {
      setRules(prev => [...prev, newRule]);
    }

    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    resetForm();
  };

  const handleDeleteRule = (ruleId: number) => {
    if (confirm('确定要删除此通知规则吗？')) {
      setRules(prev => prev.filter(r => r.id !== ruleId));
    }
  };

  const toggleRuleEnabled = (ruleId: number) => {
    setRules(prev => prev.map(r => 
      r.id === ruleId ? { ...r, isEnabled: !r.isEnabled } : r
    ));
  };

  // Filter rules
  const filteredRules = rules.filter(rule => {
    if (filterProductGroup && rule.productGroupId !== filterProductGroup) {
      return false;
    }
    if (searchProduct && !rule.productName.toLowerCase().includes(searchProduct.toLowerCase())) {
      return false;
    }
    return true;
  });

  // Filter products for selection
  const availableProducts = products.filter(product => {
    if (filterProductGroup && product.productGroupId !== filterProductGroup) {
      return false;
    }
    return true;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 text-xl">🔔</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">库存通知设置</h1>
                <p className="text-gray-600 mt-1">设置产品库存低于指定数量时的通知规则</p>
              </div>
            </div>
            <Link href="/products" className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              ← 返回产品页面
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

      {/* Filters and Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">产品组筛选</label>
              <select
                value={filterProductGroup}
                onChange={(e) => setFilterProductGroup(Number(e.target.value))}
                className="w-48 h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
              >
                <option value={0}>全部产品组</option>
                {productGroups.map(group => (
                  <option key={group.id} value={group.id}>{group.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">搜索产品</label>
              <Input
                value={searchProduct}
                onChange={(e) => setSearchProduct(e.target.value)}
                placeholder="按产品名称搜索..."
                className="w-48"
              />
            </div>
          </div>
          <Button onClick={handleAddRule} className="bg-blue-600 hover:bg-blue-700">
            添加通知规则
          </Button>
        </div>
      </div>

      {/* Rules Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品组</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">阈值</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知方式</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRules.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-8 text-center text-gray-500">
                    暂无通知规则
                  </td>
                </tr>
              ) : (
                filteredRules.map((rule) => (
                  <tr key={rule.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={rule.isEnabled}
                          onChange={() => toggleRuleEnabled(rule.id!)}
                          className="h-4 w-4 text-blue-600 rounded border-gray-300"
                        />
                        <span className={`ml-2 text-sm ${rule.isEnabled ? 'text-green-600' : 'text-gray-400'}`}>
                          {rule.isEnabled ? '启用' : '禁用'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{rule.productName}</div>
                      {rule.productBarcode && (
                        <div className="text-sm text-gray-500">{rule.productBarcode}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {rule.productGroupName || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {rule.thresholdQuantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {rule.notificationMethod === 'email' && '邮件'}
                      {rule.notificationMethod === 'dashboard' && '仪表板'}
                      {rule.notificationMethod === 'both' && '邮件+仪表板'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rule.createdAt ? new Date(rule.createdAt).toLocaleDateString() : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEditRule(rule)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => handleDeleteRule(rule.id!)}
                        className="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Rule Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>添加库存通知规则</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">选择产品 *</label>
              <select
                value={selectedProductId}
                onChange={(e) => setSelectedProductId(Number(e.target.value))}
                className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
                required
              >
                <option value={0}>请选择产品</option>
                {availableProducts.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name} {product.barcode ? `(${product.barcode})` : ''}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">库存阈值 *</label>
              <Input
                type="number"
                value={thresholdQuantity}
                onChange={(e) => setThresholdQuantity(Number(e.target.value))}
                placeholder="当库存低于此数量时发送通知"
                min="0"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">通知方式 *</label>
              <select
                value={notificationMethod}
                onChange={(e) => setNotificationMethod(e.target.value as 'email' | 'dashboard' | 'both')}
                className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
              >
                <option value="dashboard">仪表板通知</option>
                <option value="email">邮件通知</option>
                <option value="both">仪表板+邮件通知</option>
              </select>
            </div>

            {(notificationMethod === 'email' || notificationMethod === 'both') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">邮件接收者</label>
                <Input
                  value={recipients}
                  onChange={(e) => setRecipients(e.target.value)}
                  placeholder="多个邮箱用逗号分隔，例如: <EMAIL>, <EMAIL>"
                />
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={isEnabled}
                onChange={(e) => setIsEnabled(e.target.checked)}
              />
              <label className="text-sm font-medium text-gray-700">启用此通知规则</label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveRule} className="bg-blue-600 hover:bg-blue-700">
              保存规则
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Rule Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑库存通知规则</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">选择产品 *</label>
              <select
                value={selectedProductId}
                onChange={(e) => setSelectedProductId(Number(e.target.value))}
                className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
                required
              >
                <option value={0}>请选择产品</option>
                {availableProducts.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name} {product.barcode ? `(${product.barcode})` : ''}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">库存阈值 *</label>
              <Input
                type="number"
                value={thresholdQuantity}
                onChange={(e) => setThresholdQuantity(Number(e.target.value))}
                placeholder="当库存低于此数量时发送通知"
                min="0"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">通知方式 *</label>
              <select
                value={notificationMethod}
                onChange={(e) => setNotificationMethod(e.target.value as 'email' | 'dashboard' | 'both')}
                className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm"
              >
                <option value="dashboard">仪表板通知</option>
                <option value="email">邮件通知</option>
                <option value="both">仪表板+邮件通知</option>
              </select>
            </div>

            {(notificationMethod === 'email' || notificationMethod === 'both') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">邮件接收者</label>
                <Input
                  value={recipients}
                  onChange={(e) => setRecipients(e.target.value)}
                  placeholder="多个邮箱用逗号分隔，例如: <EMAIL>, <EMAIL>"
                />
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={isEnabled}
                onChange={(e) => setIsEnabled(e.target.checked)}
              />
              <label className="text-sm font-medium text-gray-700">启用此通知规则</label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveRule} className="bg-blue-600 hover:bg-blue-700">
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </div>
  );
}