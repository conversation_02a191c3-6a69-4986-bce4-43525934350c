'use client';

import { useState, useEffect } from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { ApiService } from '@/lib/api';
import { InventoryCountItem, InventoryCountFilters, Operator, ProductGroup } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingIndicator } from '@/components/ui/loading';

export default function InventoryCountHistoryPage() {
  const { canAccessInventoryCount, isLoading: permissionsLoading } = usePermissions();
  const [items, setItems] = useState<InventoryCountItem[]>([]);
  const [operators, setOperators] = useState<Operator[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [filters, setFilters] = useState<InventoryCountFilters>({
    dateFrom: '',
    dateTo: '',
    productGroupId: undefined,
    operatorId: undefined,
    status: undefined,
    hasDiscrepancy: undefined,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!permissionsLoading && canAccessInventoryCount) {
      loadInitialData();
    }
  }, [permissionsLoading, canAccessInventoryCount]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [operatorsResponse, productGroupsResponse] = await Promise.all([
        ApiService.fetchOperators(),
        ApiService.fetchProductGroups(),
      ]);

      if (operatorsResponse.result) {
        console.log('操作员API响应:', operatorsResponse.result);
        let operatorsData = [];
        if (Array.isArray(operatorsResponse.result)) {
          operatorsData = operatorsResponse.result;
        } else if (operatorsResponse.result && typeof operatorsResponse.result === 'object') {
          const result = operatorsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            operatorsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            operatorsData = result.items;
          }
        }
        setOperators(operatorsData);
      }

      if (productGroupsResponse.result) {
        console.log('产品组API响应:', productGroupsResponse.result);
        let productGroupsData = [];
        if (Array.isArray(productGroupsResponse.result)) {
          productGroupsData = productGroupsResponse.result;
        } else if (productGroupsResponse.result && typeof productGroupsResponse.result === 'object') {
          const result = productGroupsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            productGroupsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productGroupsData = result.items;
          }
        }
        setProductGroups(productGroupsData);
      }

      // 设置默认日期范围（最近30天）
      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      const defaultFilters = {
        ...filters,
        dateFrom: thirtyDaysAgo.toISOString().split('T')[0],
        dateTo: today.toISOString().split('T')[0],
      };
      
      setFilters(defaultFilters);
      await loadHistory(defaultFilters);
    } catch (err) {
      console.error('加载初始数据失败:', err);
      setError(err instanceof Error ? err.message : '加载初始数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const loadHistory = async (searchFilters: InventoryCountFilters = filters) => {
    try {
      setError(null);
      const response = await ApiService.getInventoryCountHistory(searchFilters);
      
      if (response.result) {
        setItems(Array.isArray(response.result) ? response.result : []);
      }
    } catch (err) {
      console.error('加载盘点历史失败:', err);
      setError(err instanceof Error ? err.message : '加载盘点历史失败');
    }
  };

  const handleFilterChange = (key: keyof InventoryCountFilters, value: any) => {
    const newFilters = { ...filters, [key]: value === '' ? undefined : value };
    setFilters(newFilters);
  };

  const handleSearch = () => {
    loadHistory();
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setError(null);

      const blob = await ApiService.exportInventoryCountReport(filters);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `inventory-count-report-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('导出报告失败:', err);
      setError(err instanceof Error ? err.message : '导出报告失败');
    } finally {
      setIsExporting(false);
    }
  };

  const getStatistics = () => {
    const total = items.length;
    const withDiscrepancy = items.filter(item => item.difference !== 0).length;
    const totalDiscrepancy = items.reduce((sum, item) => sum + Math.abs(item.difference), 0);
    
    return { total, withDiscrepancy, totalDiscrepancy };
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="检查权限中..." />
      </div>
    );
  }

  if (!canAccessInventoryCount) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-6xl mb-4">🚫</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">访问被拒绝</h3>
        <p className="text-gray-500">您没有权限访问库存盘点历史页面</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="加载历史记录中..." />
      </div>
    );
  }

  const stats = getStatistics();

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">盘点历史记录</h1>
              <p className="text-gray-600 mt-1">
                总记录: {stats.total} | 有差异: {stats.withDiscrepancy} | 总差异量: {stats.totalDiscrepancy}
              </p>
            </div>
            <Button
              onClick={handleExport}
              disabled={isExporting}
              variant="outline"
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  导出中...
                </>
              ) : (
                <>
                  <span className="mr-2">📊</span>
                  导出报告
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <div className="text-red-600 text-xl mr-3">❌</div>
                <div>
                  <h3 className="text-red-800 font-medium">错误</h3>
                  <p className="text-red-700 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 筛选条件 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                <Input
                  type="date"
                  value={filters.dateFrom || ''}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                <Input
                  type="date"
                  value={filters.dateTo || ''}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">产品组</label>
                <select
                  value={filters.productGroupId || ''}
                  onChange={(e) => handleFilterChange('productGroupId', e.target.value ? parseInt(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部产品组</option>
                  {productGroups.map(group => (
                    <option key={group.id} value={group.id}>{group.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">操作员</label>
                <select
                  value={filters.operatorId || ''}
                  onChange={(e) => handleFilterChange('operatorId', e.target.value ? parseInt(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部操作员</option>
                  {operators.map(operator => (
                    <option key={operator.id} value={operator.id}>
                      {operator.firstName} {operator.lastName}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部状态</option>
                  <option value="draft">草稿</option>
                  <option value="submitted">已提交</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">差异筛选</label>
                <select
                  value={filters.hasDiscrepancy === undefined ? '' : filters.hasDiscrepancy.toString()}
                  onChange={(e) => handleFilterChange('hasDiscrepancy', e.target.value === '' ? undefined : e.target.value === 'true')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部记录</option>
                  <option value="true">仅有差异</option>
                  <option value="false">仅无差异</option>
                </select>
              </div>
              <div className="flex items-end">
                <Button onClick={handleSearch} className="w-full">
                  <span className="mr-2">🔍</span>
                  搜索
                </Button>
              </div>
            </div>
          </div>

          {/* 历史记录列表 */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    产品信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    系统库存
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    盘点数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    差异
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作员
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    盘点时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {items.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center">
                      <div className="text-gray-400 text-6xl mb-4">📋</div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到记录</h3>
                      <p className="text-gray-500">请调整筛选条件或检查日期范围</p>
                    </td>
                  </tr>
                ) : (
                  items.map((item, index) => (
                    <tr key={index} className={item.difference !== 0 ? 'bg-red-50' : ''}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                          <div className="text-sm text-gray-500">{item.productGroupName}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.systemStock}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.countedStock ?? '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${
                          item.difference === 0 
                            ? 'text-green-600' 
                            : item.difference > 0 
                              ? 'text-red-600' 
                              : 'text-blue-600'
                        }`}>
                          {item.difference > 0 ? '+' : ''}{item.difference}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.operatorName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.countDate} {item.countTime}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.status === 'submitted' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.status === 'submitted' ? '已提交' : '草稿'}
                        </span>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
