'use client';

import { useEffect, useRef, useCallback } from 'react';

interface InfiniteScrollProps {
  children: React.ReactNode;
  hasMore: boolean;
  isLoading: boolean;
  onLoadMore: () => void;
  threshold?: number;
  className?: string;
}

export function InfiniteScroll({
  children,
  hasMore,
  isLoading,
  onLoadMore,
  threshold = 200,
  className = '',
}: InfiniteScrollProps) {
  const loadingRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);

  // 防抖和防重复调用的onLoadMore
  const throttledLoadMore = useCallback(() => {
    if (isLoadingRef.current || isLoading || !hasMore) {
      console.log('跳过loadMore调用:', {
        isLoadingRef: isLoadingRef.current,
        isLoading,
        hasMore
      });
      return;
    }

    console.log('触发loadMore');
    isLoadingRef.current = true;
    onLoadMore();

    // 500ms后重置标志，防止过于频繁的调用
    setTimeout(() => {
      isLoadingRef.current = false;
    }, 500);
  }, [isLoading, hasMore, onLoadMore]);

  // 只使用Intersection Observer，移除滚动事件监听
  useEffect(() => {
    const loadingElement = loadingRef.current;
    if (!loadingElement || !hasMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        console.log('Intersection Observer触发:', {
          isIntersecting: target.isIntersecting,
          isLoading,
          hasMore
        });

        if (target.isIntersecting && !isLoading && hasMore && !isLoadingRef.current) {
          throttledLoadMore();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px', // 增加rootMargin，提前触发
      }
    );

    observer.observe(loadingElement);

    return () => {
      observer.unobserve(loadingElement);
    };
  }, [hasMore, isLoading, throttledLoadMore]);

  // 重置加载标志当isLoading状态变化时
  useEffect(() => {
    if (!isLoading) {
      isLoadingRef.current = false;
    }
  }, [isLoading]);

  return (
    <div className={`space-y-4 ${className}`}>
      {children}

      {/* 加载更多指示器 */}
      {hasMore && (
        <div
          ref={loadingRef}
          className="flex justify-center items-center py-4"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2 text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm">加载更多...</span>
            </div>
          ) : (
            <div className="text-gray-400 text-sm">滚动加载更多</div>
          )}
        </div>
      )}

      {/* 没有更多数据的提示 */}
      {!hasMore && (
        <div className="text-center py-4 text-gray-400 text-sm">
          已显示所有结果
        </div>
      )}
    </div>
  );
}
