'use client';

import { useState } from 'react';
import { ApiService } from '@/lib/api';
import { useAuth } from '@/hooks/use-auth';

export default function TestPage() {
  const { isAuthenticated, token, login } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testLogin = async () => {
    if (!username || !password) {
      addResult('错误: 请输入用户名和密码');
      return;
    }

    setIsLoading(true);
    try {
      addResult(`开始测试登录，用户名: ${username}`);
      const response = await ApiService.login({
        username: username,
        password: password
      });
      addResult(`登录成功: ${JSON.stringify(response)}`);
      
      // 如果登录成功，更新认证状态
      if (response.result?.token) {
        await login({ username, password });
        addResult('认证状态已更新');
      }
    } catch (error) {
      addResult(`登录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testProductGroups = async () => {
    setIsLoading(true);
    try {
      addResult('开始测试产品组API...');
      const response = await ApiService.fetchProductGroups();
      addResult(`产品组API成功: ${JSON.stringify(response)}`);
      
      // 详细分析响应结构
      if (response && response.result) {
        addResult(`产品组result类型: ${typeof response.result}`);
        addResult(`产品组result是否为数组: ${Array.isArray(response.result)}`);
        addResult(`产品组result内容: ${JSON.stringify(response.result)}`);
        
        const result = response.result as any;
        if (result.data) {
          addResult(`发现result.data: ${JSON.stringify(result.data)}`);
        }
        if (result.items) {
          addResult(`发现result.items: ${JSON.stringify(result.items)}`);
        }
        if (result.productGroups) {
          addResult(`发现result.productGroups: ${JSON.stringify(result.productGroups)}`);
        }
      }
    } catch (error) {
      addResult(`产品组API失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testProducts = async () => {
    setIsLoading(true);
    try {
      addResult('开始测试产品API...');
      const response = await ApiService.fetchAllProducts();
      addResult(`产品API成功: ${JSON.stringify(response)}`);
      
      // 详细分析响应结构
      if (response && response.result) {
        addResult(`产品result类型: ${typeof response.result}`);
        addResult(`产品result是否为数组: ${Array.isArray(response.result)}`);
        addResult(`产品result内容: ${JSON.stringify(response.result)}`);
        
        const result = response.result as any;
        if (result.data) {
          addResult(`发现result.data: ${JSON.stringify(result.data)}`);
        }
        if (result.items) {
          addResult(`发现result.items: ${JSON.stringify(result.items)}`);
        }
        if (result.products) {
          addResult(`发现result.products: ${JSON.stringify(result.products)}`);
        }
      }
    } catch (error) {
      addResult(`产品API失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">API 测试页面</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">认证状态</h2>
          <p>已认证: {isAuthenticated ? '是' : '否'}</p>
          <p>Token: {token ? '存在' : '不存在'}</p>
          {token && <p className="text-xs text-gray-500 mt-2">Token: {token.substring(0, 50)}...</p>}
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">登录测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入用户名"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入密码"
              />
            </div>
          </div>
          <button
            onClick={testLogin}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            测试登录
          </button>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API 测试</h2>
          <div className="space-x-4">
            <button
              onClick={testProductGroups}
              disabled={isLoading || !isAuthenticated}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              测试产品组
            </button>
            <button
              onClick={testProducts}
              disabled={isLoading || !isAuthenticated}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              测试产品
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              清除结果
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">测试结果</h2>
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">暂无测试结果</p>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-2 p-2 bg-white rounded border">
                  <pre className="text-sm whitespace-pre-wrap">{result}</pre>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">使用说明</h3>
          <ol className="text-blue-700 space-y-1 text-sm">
            <li>1. 首先输入正确的用户名和密码进行登录测试</li>
            <li>2. 登录成功后，认证状态会更新</li>
            <li>3. 然后可以测试产品组和产品API</li>
            <li>4. 查看测试结果了解API调用情况</li>
            <li>5. 如果API调用失败，检查网络连接和服务器状态</li>
          </ol>
        </div>
      </div>
    </div>
  );
} 