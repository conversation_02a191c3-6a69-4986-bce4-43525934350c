// 模拟库存盘点API服务 - 用于演示功能，直到后端API实现
import {
  InventoryCountConfig,
  InventoryCountSession,
  InventoryCountItem,
  InventoryCountFilters,
  ApiResponse,
  Product,
  ProductGroup,
  InventoryCountConfigProduct,
  InventoryCountRecord
} from './types';
import { ApiService } from './api';

// 模拟数据存储
let mockConfig: InventoryCountConfig = {
  id: 1,
  configName: 'Default Config',
  authorizedOperators: [2237],
  frequency: 'daily',
  scheduleTime: '09:00',
  scheduleDays: [],
  isActive: true,
  createdBy: 2237,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

let mockConfigProducts: InventoryCountConfigProduct[] = [];
let mockSessions: InventoryCountSession[] = [];
let mockItems: InventoryCountItem[] = [];
let sessionCounter = 1;

// 生成唯一的会话ID
function generateSessionId(): string {
  return `INV-${Date.now()}-${sessionCounter++}`;
}

// 模拟延迟
function delay(ms: number = 500): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export class MockInventoryAPI {
  // 获取盘点配置
  static async getInventoryCountConfig(): Promise<ApiResponse<InventoryCountConfig>> {
    await delay(300);
    return {
      result: mockConfig,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 保存盘点配置
  static async saveInventoryCountConfig(config: InventoryCountConfig): Promise<ApiResponse<InventoryCountConfig>> {
    await delay(500);
    mockConfig = {
      ...config,
      id: mockConfig.id || 1,
      updatedAt: new Date().toISOString(),
    };
    return {
      result: mockConfig,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 配置产品相关
  static async getInventoryCountConfigProducts(configId: number): Promise<ApiResponse<InventoryCountConfigProduct[]>> {
    await delay(300);
    const products = mockConfigProducts.filter(p => p.configId === configId);
    return {
      result: products,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  static async saveInventoryCountConfigProducts(products: InventoryCountConfigProduct[]): Promise<ApiResponse<InventoryCountConfigProduct[]>> {
    await delay(500);

    // 清除旧的配置产品
    if (products.length > 0) {
      const configId = products[0].configId;
      mockConfigProducts = mockConfigProducts.filter(p => p.configId !== configId);
    }

    // 添加新的配置产品
    const savedProducts = products.map((product, index) => ({
      ...product,
      id: Date.now() + index,
      createdAt: new Date().toISOString(),
    }));

    mockConfigProducts.push(...savedProducts);

    return {
      result: savedProducts,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 创建盘点会话
  static async createInventoryCountSession(): Promise<ApiResponse<InventoryCountSession>> {
    await delay(400);
    const newSession: InventoryCountSession = {
      id: generateSessionId(),
      operatorId: 2237, // 假设当前用户ID
      operatorName: 'Admin User',
      status: 'in_progress',
      startTime: new Date().toISOString(),
      totalItems: 0,
      completedItems: 0,
      discrepancyItems: 0,
      createdAt: new Date().toISOString(),
    };
    
    mockSessions.push(newSession);
    return {
      result: newSession,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 获取活跃的盘点会话
  static async getActiveInventoryCountSession(): Promise<ApiResponse<InventoryCountSession | null>> {
    await delay(200);
    const activeSession = mockSessions.find(s => s.status === 'in_progress');
    return {
      result: activeSession || null,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 获取盘点项目 - 使用真实产品数据
  static async getInventoryCountItems(sessionId: string, productGroupId?: number): Promise<ApiResponse<InventoryCountItem[]>> {
    await delay(600);

    // 如果没有现有的项目，从真实API获取产品数据
    let sessionItems = mockItems.filter(item => item.countSessionId === sessionId);

    if (sessionItems.length === 0) {
      try {
        console.log('根据配置的产品生成盘点项目');

        // 获取配置的产品
        let configProducts = mockConfigProducts.filter(p => p.configId === mockConfig.id);

        if (configProducts.length === 0) {
          console.log('没有配置的产品，从API获取产品数据');

          // 如果没有配置的产品，从API获取
          const [productsResponse, productGroupsResponse] = await Promise.all([
            fetch(`${(ApiService as any).baseURL}/products?Pagination.Limit=10000`, {
              headers: (ApiService as any).getHeaders(),
            }).then(res => res.json()),
            fetch(`${(ApiService as any).baseURL}/productgroups?Pagination.Limit=1000`, {
              headers: (ApiService as any).getHeaders(),
            }).then(res => res.json())
          ]);

          // 处理产品数据
          let products: Product[] = [];
          if (productsResponse.result) {
            if (Array.isArray(productsResponse.result)) {
              products = productsResponse.result;
            } else if (productsResponse.result.data && Array.isArray(productsResponse.result.data)) {
              products = productsResponse.result.data;
            } else if (productsResponse.result.items && Array.isArray(productsResponse.result.items)) {
              products = productsResponse.result.items;
            }
          }

          // 处理产品组数据
          let productGroups: ProductGroup[] = [];
          if (productGroupsResponse.result) {
            if (Array.isArray(productGroupsResponse.result)) {
              productGroups = productGroupsResponse.result;
            } else if (productGroupsResponse.result.data && Array.isArray(productGroupsResponse.result.data)) {
              productGroups = productGroupsResponse.result.data;
            } else if (productGroupsResponse.result.items && Array.isArray(productGroupsResponse.result.items)) {
              productGroups = productGroupsResponse.result.items;
            }
          }

          // 创建产品组名称映射
          const groupNameMap: { [key: number]: string } = {};
          productGroups.forEach(group => {
            groupNameMap[group.id] = group.name;
          });

          // 取前10个产品作为示例
          const sampleProducts = products.slice(0, 10);

          // 创建盘点项目
          sessionItems = sampleProducts.map(product => ({
            id: Math.floor(Math.random() * 10000),
            countSessionId: sessionId,
            productId: product.id,
            productName: product.name,
            productGroupId: product.productGroupId || 0,
            productGroupName: groupNameMap[product.productGroupId || 0] || '未分组',
            systemStock: Math.floor(Math.random() * 50), // 模拟库存数量
            countedStock: null,
            difference: 0,
            operatorId: 2237,
            operatorName: 'Admin User',
            status: 'draft' as const,
            countDate: new Date().toISOString().split('T')[0],
            countTime: new Date().toTimeString().split(' ')[0],
            createdAt: new Date().toISOString(),
          }));
        } else {
          console.log(`使用配置的产品，共 ${configProducts.length} 个`);

          // 根据产品组过滤
          if (productGroupId) {
            configProducts = configProducts.filter(p => p.productGroupId === productGroupId);
          }

          // 获取库存信息
          const stockPromises = configProducts.map(async (product) => {
            try {
              const stockResponse = await fetch(`${(ApiService as any).baseURL}/productsstock/${product.productId}`, {
                headers: (ApiService as any).getHeaders(),
              });

              if (stockResponse.ok) {
                const stockData = await stockResponse.json();
                return stockData.result?.stock || 0;
              }
            } catch (error) {
              console.log(`获取产品 ${product.productId} 库存失败，使用随机数`);
            }
            return Math.floor(Math.random() * 50);
          });

          const stockValues = await Promise.all(stockPromises);

          // 创建盘点项目
          sessionItems = configProducts.map((product, index) => ({
            id: Math.floor(Math.random() * 10000),
            countSessionId: sessionId,
            productId: product.productId,
            productName: product.productName,
            productGroupId: product.productGroupId || 0,
            productGroupName: product.productGroupName || '未分组',
            systemStock: stockValues[index],
            countedStock: null,
            difference: 0,
            operatorId: 2237,
            operatorName: 'Admin User',
            status: 'draft' as const,
            countDate: new Date().toISOString().split('T')[0],
            countTime: new Date().toTimeString().split(' ')[0],
            createdAt: new Date().toISOString(),
          }));
        }

        mockItems.push(...sessionItems);

      } catch (error) {
        console.error('获取真实产品数据失败，使用备用模拟数据:', error);

        // 如果API调用失败，使用备用的模拟数据
        const mockProducts = [
          { id: 3589, name: 'MOTI LYCHEE ICE', groupId: 67, groupName: 'NANOSTIX', stock: 25 },
          { id: 3590, name: 'MOTI GRAPE ICE', groupId: 67, groupName: 'NANOSTIX', stock: 18 },
          { id: 3591, name: 'MOTI MINT ICE', groupId: 67, groupName: 'NANOSTIX', stock: 32 },
          { id: 3592, name: 'VAPE PEN BLUE', groupId: 68, groupName: 'VAPE PENS', stock: 15 },
          { id: 3593, name: 'VAPE PEN RED', groupId: 68, groupName: 'VAPE PENS', stock: 22 },
          { id: 3594, name: 'JUICE APPLE', groupId: 69, groupName: 'E-LIQUIDS', stock: 45 },
          { id: 3595, name: 'JUICE BERRY', groupId: 69, groupName: 'E-LIQUIDS', stock: 38 },
        ];

        sessionItems = mockProducts
          .filter(product => !productGroupId || product.groupId === productGroupId)
          .map(product => ({
            id: Math.floor(Math.random() * 10000),
            countSessionId: sessionId,
            productId: product.id,
            productName: product.name,
            productGroupId: product.groupId,
            productGroupName: product.groupName,
            systemStock: product.stock,
            countedStock: null,
            difference: 0,
            operatorId: 2237,
            operatorName: 'Admin User',
            status: 'draft' as const,
            countDate: new Date().toISOString().split('T')[0],
            countTime: new Date().toTimeString().split(' ')[0],
            createdAt: new Date().toISOString(),
          }));

        mockItems.push(...sessionItems);
      }
    }

    // 如果指定了产品组，进行过滤
    if (productGroupId) {
      sessionItems = sessionItems.filter(item => item.productGroupId === productGroupId);
    }

    return {
      result: sessionItems,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 保存盘点项目
  static async saveInventoryCountItem(item: Partial<InventoryCountItem>): Promise<ApiResponse<InventoryCountItem>> {
    await delay(300);
    
    const existingIndex = mockItems.findIndex(
      existing => existing.countSessionId === item.countSessionId && existing.productId === item.productId
    );

    let savedItem: InventoryCountItem;

    if (existingIndex >= 0) {
      // 更新现有项目
      savedItem = {
        ...mockItems[existingIndex],
        ...item,
        updatedAt: new Date().toISOString(),
      } as InventoryCountItem;
      mockItems[existingIndex] = savedItem;
    } else {
      // 创建新项目
      savedItem = {
        id: Math.floor(Math.random() * 10000),
        countSessionId: item.countSessionId!,
        productId: item.productId!,
        productName: item.productName!,
        productGroupId: item.productGroupId!,
        productGroupName: item.productGroupName!,
        systemStock: item.systemStock!,
        countedStock: item.countedStock!,
        difference: item.difference!,
        operatorId: item.operatorId!,
        operatorName: item.operatorName!,
        status: item.status!,
        countDate: item.countDate!,
        countTime: item.countTime!,
        createdAt: new Date().toISOString(),
        ...item,
      } as InventoryCountItem;
      mockItems.push(savedItem);
    }

    return {
      result: savedItem,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 提交盘点会话
  static async submitInventoryCountSession(sessionId: string): Promise<ApiResponse<InventoryCountSession>> {
    await delay(800);
    
    const sessionIndex = mockSessions.findIndex(s => s.id === sessionId);
    if (sessionIndex >= 0) {
      mockSessions[sessionIndex] = {
        ...mockSessions[sessionIndex],
        status: 'completed',
        endTime: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // 更新所有相关项目为已提交状态
      mockItems = mockItems.map(item => 
        item.countSessionId === sessionId 
          ? { ...item, status: 'submitted' as const }
          : item
      );

      return {
        result: mockSessions[sessionIndex],
        httpStatusCode: 200,
        message: null,
        isError: false,
      };
    }

    throw new Error('会话未找到');
  }

  // 获取盘点历史
  static async getInventoryCountHistory(filters: InventoryCountFilters): Promise<ApiResponse<InventoryCountItem[]>> {
    await delay(400);
    
    let filteredItems = [...mockItems];

    // 应用过滤条件
    if (filters.dateFrom) {
      filteredItems = filteredItems.filter(item => item.countDate >= filters.dateFrom!);
    }
    
    if (filters.dateTo) {
      filteredItems = filteredItems.filter(item => item.countDate <= filters.dateTo!);
    }
    
    if (filters.productGroupId) {
      filteredItems = filteredItems.filter(item => item.productGroupId === filters.productGroupId);
    }
    
    if (filters.operatorId) {
      filteredItems = filteredItems.filter(item => item.operatorId === filters.operatorId);
    }
    
    if (filters.status) {
      filteredItems = filteredItems.filter(item => item.status === filters.status);
    }
    
    if (filters.hasDiscrepancy !== undefined) {
      filteredItems = filteredItems.filter(item => 
        filters.hasDiscrepancy ? item.difference !== 0 : item.difference === 0
      );
    }

    return {
      result: filteredItems,
      httpStatusCode: 200,
      message: null,
      isError: false,
    };
  }

  // 导出报告（模拟）
  static async exportInventoryCountReport(filters: InventoryCountFilters): Promise<Blob> {
    await delay(1000);
    
    // 创建一个简单的CSV内容
    const items = await this.getInventoryCountHistory(filters);
    const csvContent = [
      'Product Name,Product Group,System Stock,Counted Stock,Difference,Operator,Date,Time,Status',
      ...items.result!.map(item => 
        `${item.productName},${item.productGroupName},${item.systemStock},${item.countedStock || ''},${item.difference},${item.operatorName},${item.countDate},${item.countTime},${item.status}`
      )
    ].join('\n');

    return new Blob([csvContent], { type: 'text/csv' });
  }
}
