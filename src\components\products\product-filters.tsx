'use client';

import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ApiService } from '@/lib/api';
import { ProductGroup, type ProductFilters } from '@/lib/types';
import { debounce } from '@/lib/utils';

interface ProductFiltersProps {
  onFiltersChange?: (filters: ProductFilters) => void;
  onProductGroupSelect?: (productGroupId: number | undefined) => void;
}

export function ProductFilters({ onFiltersChange, onProductGroupSelect }: ProductFiltersProps) {
  const [filters, setFilters] = useState<ProductFilters>({});
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [isLoadingGroups, setIsLoadingGroups] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 增强防抖搜索 - 增加延迟时间，减少API调用
  const debouncedSearch = debounce((search: string) => {
    console.log('防抖函数执行，设置搜索条件:', search);
    const trimmedSearch = search.trim() || undefined;
    setFilters(prev => {
      const newFilters = { ...prev, search: trimmedSearch };
      console.log('更新过滤条件:', newFilters);
      return newFilters;
    });
    setIsSearching(false); // 搜索完成
  }, 500); // 增加到500ms

  useEffect(() => {
    loadProductGroups();
  }, []);

  useEffect(() => {
    onFiltersChange?.(filters);
  }, [filters, onFiltersChange]);

  const loadProductGroups = async () => {
    try {
      setIsLoadingGroups(true);
      setDebugInfo('正在加载产品组...');
      
      console.log('开始加载产品组...');
      const response = await ApiService.fetchProductGroups();
      console.log('产品组API响应:', response);
      
      // 检查响应格式
      if (response && response.result) {
        console.log('产品组result内容:', response.result);
        
        // 尝试不同的数据访问方式
        let productGroupsData = null;
        const result = response.result as any;
        
        if (Array.isArray(result)) {
          productGroupsData = result;
        } else if (result.data && Array.isArray(result.data)) {
          productGroupsData = result.data;
        } else if (result.items && Array.isArray(result.items)) {
          productGroupsData = result.items;
        } else if (result.productGroups && Array.isArray(result.productGroups)) {
          productGroupsData = result.productGroups;
        } else {
          // 如果都不是数组，尝试将整个result作为单个对象处理
          console.log('尝试将result作为单个产品组处理');
          productGroupsData = [result];
        }
        
        if (productGroupsData && Array.isArray(productGroupsData)) {
          setProductGroups(productGroupsData);
          setDebugInfo(`成功加载 ${productGroupsData.length} 个产品组`);
          console.log('产品组数据:', productGroupsData);
        } else {
          setProductGroups([]);
          setDebugInfo('产品组数据格式错误 - 无法解析为数组');
          console.error('产品组数据格式错误 - 无法解析为数组:', response.result);
        }
      } else {
        setProductGroups([]);
        setDebugInfo('产品组响应格式错误');
        console.error('产品组响应格式错误:', response);
      }
    } catch (error) {
      console.error('加载产品组失败:', error);
      setProductGroups([]);
      setDebugInfo(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoadingGroups(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value;
    console.log('搜索输入变化:', search);

    // 清除之前的搜索状态定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果有输入内容，显示搜索状态
    if (search.trim()) {
      setIsSearching(true);
      // 设置超时，如果搜索太久就隐藏状态
      searchTimeoutRef.current = setTimeout(() => {
        setIsSearching(false);
      }, 2000);
    } else {
      setIsSearching(false);
    }

    console.log('调用防抖搜索:', search);
    debouncedSearch(search);
  };

  const handleGroupChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productGroupId = e.target.value ? Number(e.target.value) : undefined;
    setFilters(prev => ({ ...prev, productGroupId }));
    
    // 通知父组件产品组选择变化
    onProductGroupSelect?.(productGroupId);
  };

  const handleStockFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, inStockOnly: e.target.checked }));
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, sortBy: e.target.value || undefined }));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* 调试信息 */}
      {debugInfo && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">{debugInfo}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* 产品组选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            产品组
          </label>
          <select
            value={filters.productGroupId || ''}
            onChange={handleGroupChange}
            className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isLoadingGroups}
          >
            <option value="" className="text-black">请选择产品组</option>
            {Array.isArray(productGroups) && productGroups.map(group => (
              <option key={group.id} value={group.id} className="text-black">
                {group.name}
              </option>
            ))}
          </select>
          {isLoadingGroups && (
            <p className="text-xs text-gray-500 mt-1">加载中...</p>
          )}
        </div>

        {/* 搜索框 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            搜索
            {isSearching && (
              <span className="ml-2 text-xs text-blue-600">
                <span className="animate-pulse">搜索中...</span>
              </span>
            )}
          </label>
          <div className="relative">
            <Input
              type="text"
              placeholder="按名称、条码搜索..."
              onChange={handleSearchChange}
              className="w-full text-black pr-8"
            />
            {isSearching && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>
        </div>

        {/* 库存筛选 */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="inStockFilter"
            checked={filters.inStockOnly || false}
            onChange={handleStockFilterChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="inStockFilter" className="text-sm font-medium text-gray-700">
            仅显示有库存
          </label>
        </div>

        {/* 排序 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            排序
          </label>
          <select
            value={filters.sortBy || ''}
            onChange={handleSortChange}
            className="w-full h-10 rounded-lg border border-gray-300 px-3 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="" className="text-black">默认排序</option>
            <option value="name_asc" className="text-black">产品名称 A→Z</option>
            <option value="name_desc" className="text-black">产品名称 Z→A</option>
            <option value="stock_desc" className="text-black">库存 多→少</option>
            <option value="stock_asc" className="text-black">库存 少→多</option>
            <option value="price_desc" className="text-black">价格 高→低</option>
            <option value="price_asc" className="text-black">价格 低→高</option>
            <option value="id_desc" className="text-black">ID 新→旧</option>
            <option value="id_asc" className="text-black">ID 旧→新</option>
          </select>
        </div>
      </div>
    </div>
  );
} 