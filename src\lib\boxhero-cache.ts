import { BoxHeroApiService } from './boxhero-api';
import { BoxHeroItem } from './types';

interface BoxHeroCacheEntry {
  quantity: number;
  timestamp: number;
  itemId: string;
  itemName: string;
}

class BoxHeroCache {
  private cache = new Map<string, BoxHeroCacheEntry>();
  private allItemsCache: BoxHeroItem[] | null = null;
  private allItemsTimestamp: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 延长到10分钟缓存，减少API调用
  private pendingRequests = new Map<string, Promise<number>>();
  private allItemsRequest: Promise<BoxHeroItem[]> | null = null;

  // 获取指定barcode的库存数量
  async getQuantityByBarcode(barcode: string): Promise<number> {
    if (!barcode) return 0;

    // 检查缓存
    const cached = this.cache.get(barcode);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.quantity;
    }

    // 检查是否有正在进行的请求
    const pendingRequest = this.pendingRequests.get(barcode);
    if (pendingRequest) {
      return pendingRequest;
    }

    // 创建新的请求
    const request = this.fetchQuantityFromAPI(barcode);
    this.pendingRequests.set(barcode, request);

    try {
      const quantity = await request;
      return quantity;
    } finally {
      // 清除待处理的请求
      this.pendingRequests.delete(barcode);
    }
  }

  private async fetchQuantityFromAPI(barcode: string): Promise<number> {
    try {
      console.log(`正在获取条码 ${barcode} 的BoxHero库存...`);
      const item = await BoxHeroApiService.getItemByBarcode(barcode);
      
      console.log(`条码 ${barcode} 的查找结果:`, item ? {
        id: item.id,
        name: item.name,
        barcode: item.barcode,
        quantity: item.quantity,
        quantityType: typeof item.quantity
      } : '未找到匹配项');
      
      if (item) {
        const quantity = item.quantity || 0;
        // 缓存结果
        this.cache.set(barcode, {
          quantity: quantity,
          timestamp: Date.now(),
          itemId: item.id,
          itemName: item.name
        });
        console.log(`条码 ${barcode} 的库存量: ${quantity}`);
        return quantity;
      } else {
        // 缓存0值，避免重复请求
        this.cache.set(barcode, {
          quantity: 0,
          timestamp: Date.now(),
          itemId: '',
          itemName: ''
        });
        console.log(`条码 ${barcode} 未找到匹配项，返回0`);
        return 0;
      }
    } catch (error) {
      console.error(`获取BoxHero库存失败 - 条码: ${barcode}`, error);
      return 0;
    }
  }

  // 批量获取多个barcode的库存数量 (优化后：使用特定item端点)
  async getBatchQuantities(barcodes: string[]): Promise<Map<string, number>> {
    const results = new Map<string, number>();
    const validBarcodes = barcodes.filter(barcode => barcode && barcode.trim());
    
    if (validBarcodes.length === 0) {
      return results;
    }

    try {
      console.log(`BoxHero批量获取库存 - 条码数量: ${validBarcodes.length}`);
      
      // 使用优化的API方法
      const itemsMap = await BoxHeroApiService.getItemsByBarcodes(validBarcodes);
      
      // 处理结果并更新缓存
      validBarcodes.forEach(barcode => {
        const item = itemsMap.get(barcode);
        const quantity = item ? (item.quantity || 0) : 0;
        
        console.log(`条码 ${barcode}: ${item ? '找到' : '未找到'}, 库存: ${quantity}`);
        
        results.set(barcode, quantity);
        
        // 更新缓存
        this.cache.set(barcode, {
          quantity,
          timestamp: Date.now(),
          itemId: item?.id || '',
          itemName: item?.name || ''
        });
      });

      console.log(`BoxHero批量获取完成 - 成功获取 ${results.size} 个库存信息`);
      return results;
    } catch (error) {
      console.error('批量获取BoxHero库存失败:', error);
      
      // 返回所有0值
      validBarcodes.forEach(barcode => {
        results.set(barcode, 0);
      });
      
      return results;
    }
  }

  // 获取所有items（带缓存）- 已弃用，改为使用特定端点
  private async getAllItems(): Promise<BoxHeroItem[]> {
    console.warn('getAllItems方法已弃用，建议使用优化的特定item端点');
    return [];
  }

  // 获取缓存的数量（同步方法，只返回已缓存的数据）
  getCachedQuantity(barcode: string): number | null {
    if (!barcode) return null;
    
    const cached = this.cache.get(barcode);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.quantity;
    }
    return null;
  }

  // 预加载指定barcodes的库存数据（优化后：使用特定端点）
  async preloadQuantities(barcodes: string[]): Promise<void> {
    const validBarcodes = barcodes.filter(barcode => barcode && barcode.trim());
    if (validBarcodes.length === 0) return;

    // 检查哪些条码还没有有效缓存
    const uncachedBarcodes = validBarcodes.filter(barcode => {
      const cached = this.cache.get(barcode);
      return !cached || Date.now() - cached.timestamp >= this.CACHE_DURATION;
    });

    console.log(`BoxHero预加载: 总条码${validBarcodes.length}个，需要加载${uncachedBarcodes.length}个`);

    if (uncachedBarcodes.length === 0) {
      console.log('BoxHero预加载: 所有条码都有有效缓存，跳过API调用');
      return;
    }

    try {
      // 使用优化的批量查询方法
      await this.getBatchQuantities(uncachedBarcodes);
      console.log(`BoxHero预加载完成: ${uncachedBarcodes.length}个条码`);
    } catch (error) {
      console.error('预加载BoxHero库存失败:', error);
    }
  }

  // 清除缓存
  clearCache(): void {
    this.cache.clear();
    // 同时清除API服务的映射缓存
    BoxHeroApiService.clearMappingCache();
    this.allItemsCache = null;
    this.allItemsTimestamp = 0;
    console.log('BoxHero缓存和映射已清除');
  }

  // 清除特定barcode的缓存
  clearBarcodeCache(barcode: string): void {
    this.cache.delete(barcode);
  }

  // 获取缓存统计信息
  getCacheStats(): { 
    size: number; 
    mappingCacheInfo: ReturnType<typeof BoxHeroApiService.getMappingCacheInfo>;
    oldestEntry: number;
    newestEntry: number;
  } {
    const now = Date.now();
    let oldestEntry = now;
    let newestEntry = 0;

    this.cache.forEach((entry) => {
      if (entry.timestamp < oldestEntry) oldestEntry = entry.timestamp;
      if (entry.timestamp > newestEntry) newestEntry = entry.timestamp;
    });

    return {
      size: this.cache.size,
      mappingCacheInfo: BoxHeroApiService.getMappingCacheInfo(),
      oldestEntry: oldestEntry === now ? 0 : now - oldestEntry,
      newestEntry: newestEntry === 0 ? 0 : now - newestEntry
    };
  }
}

// 导出单例实例
export const boxHeroCache = new BoxHeroCache();