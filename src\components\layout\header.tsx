'use client';

import Link from 'next/link';
import { useAuth } from '@/hooks/use-auth';
import { usePermissions } from '@/hooks/use-permissions';
import { Button } from '@/components/ui/button';

export function Header() {
  const { logout } = useAuth();
  const { canAccessInventoryCount, canManageInventoryCount } = usePermissions();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/products" className="flex items-center space-x-2">
              <span className="text-2xl">📦</span>
              <h1 className="text-xl font-bold text-gray-900">
                产品库存管理
              </h1>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <Link href="/add-product">
              <Button variant="primary" size="sm">
                <span className="mr-2">➕</span>
                添加产品
              </Button>
            </Link>

            {canAccessInventoryCount && (
              <>
                <Link href="/inventory-count">
                  <Button variant="outline" size="sm">
                    <span className="mr-2">📋</span>
                    库存盘点
                  </Button>
                </Link>
                <Link href="/inventory-count/history">
                  <Button variant="outline" size="sm">
                    <span className="mr-2">📊</span>
                    盘点历史
                  </Button>
                </Link>
              </>
            )}

            {canManageInventoryCount && (
              <Link href="/inventory-count/admin">
                <Button variant="outline" size="sm">
                  <span className="mr-2">⚙️</span>
                  盘点管理
                </Button>
              </Link>
            )}

            {/* 库存通知导航 */}
            <Link href="/stock-notifications">
              <Button variant="outline" size="sm">
                <span className="mr-2">🔔</span>
                库存通知
              </Button>
            </Link>

            <Link href="/stock-notifications/records">
              <Button variant="outline" size="sm">
                <span className="mr-2">📝</span>
                通知记录
              </Button>
            </Link>

            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
            >
              <span className="mr-2">🚪</span>
              登出
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
} 