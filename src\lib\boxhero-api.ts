import { BoxHeroItem, BoxHeroApiResponse } from './types';
import { boxHeroRateLimiter } from './rate-limiter';

const BOXHERO_API_BASE = 'https://rest.boxhero-app.com/v1';
const BOXHERO_TOKEN = '93dac409-c794-40a5-a388-c4283002f8a5';

export class BoxHeroApiService {
  // 存储barcode到item_id的映射缓存
  private static barcodeToIdMap: Map<string, string> = new Map();
  private static idToBarcodeMap: Map<string, string> = new Map();
  private static mappingCacheTimestamp = 0;
  private static readonly MAPPING_CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存

  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // 使用速率限制器执行请求
    return boxHeroRateLimiter.execute(async () => {
      const url = `${BOXHERO_API_BASE}${endpoint}`;
      
      const fetchOptions: RequestInit = {
        ...options,
        headers: {
          'Authorization': `Bearer ${BOXHERO_TOKEN}`,
          'Content-Type': 'application/json',
          ...options.headers,
        }
      };

      console.log('BoxHero API请求:', url);
      
      // 显示队列状态
      const queueStatus = boxHeroRateLimiter.getQueueStatus();
      if (queueStatus.queueLength > 0 || queueStatus.recentRequests > 0) {
        console.log('BoxHero速率限制状态:', queueStatus);
      }

      try {
        const response = await fetch(url, fetchOptions);
        
        // 检查速率限制头部
        const rateLimitHeaders = {
          limit: response.headers.get('X-Ratelimit-Limit'),
          remaining: response.headers.get('X-Ratelimit-Remaining'),
          reset: response.headers.get('X-Ratelimit-Reset')
        };
        
        if (rateLimitHeaders.limit || rateLimitHeaders.remaining || rateLimitHeaders.reset) {
          console.log('BoxHero速率限制信息:', rateLimitHeaders);
        }
        
        if (!response.ok) {
          // 如果是速率限制错误，记录详细信息
          if (response.status === 429) {
            const resetTime = rateLimitHeaders.reset ? parseInt(rateLimitHeaders.reset) : 60;
            console.error(`BoxHero API速率限制超出，需要等待 ${resetTime} 秒`);
            throw new Error(`BoxHero API速率限制超出，请稍后重试 (${resetTime}秒后重置)`);
          }
          
          console.error('BoxHero API错误:', response.status, response.statusText);
          throw new Error(`BoxHero API请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('BoxHero API响应 - URL:', url);
        console.log('BoxHero API响应 - 数据类型:', typeof data);
        console.log('BoxHero API响应 - 是否为数组:', Array.isArray(data));
        if (Array.isArray(data) && data.length > 0) {
          console.log('BoxHero API响应 - 第一项结构:', JSON.stringify(data[0], null, 2));
        } else {
          console.log('BoxHero API响应 - 完整数据:', JSON.stringify(data, null, 2));
        }
        return data;
      } catch (error) {
        console.error('BoxHero API请求异常:', error);
        throw error;
      }
    });
  }

  // 建立并缓存barcode到item_id的映射关系
  private static async buildBarcodeMapping(): Promise<void> {
    const now = Date.now();
    
    // 检查缓存是否仍然有效
    if (this.mappingCacheTimestamp > 0 && 
        now - this.mappingCacheTimestamp < this.MAPPING_CACHE_DURATION) {
      console.log('BoxHero映射缓存仍然有效，跳过重建');
      return;
    }

    try {
      console.log('开始建立BoxHero条码到ID的映射...');
      const items = await this.getAllItems();
      
      // 清空旧缓存
      this.barcodeToIdMap.clear();
      this.idToBarcodeMap.clear();
      
      // 建立映射
      items.forEach(item => {
        if (item.barcode && item.id) {
          this.barcodeToIdMap.set(item.barcode, item.id);
          this.idToBarcodeMap.set(item.id, item.barcode);
        }
      });
      
      this.mappingCacheTimestamp = now;
      console.log(`BoxHero映射建立完成: ${this.barcodeToIdMap.size} 个条码映射`);
      
    } catch (error) {
      console.error('建立BoxHero条码映射失败:', error);
      throw error;
    }
  }

  // 根据barcode获取item_id
  private static async getItemIdByBarcode(barcode: string): Promise<string | null> {
    await this.buildBarcodeMapping();
    return this.barcodeToIdMap.get(barcode) || null;
  }

  // 获取所有items (优化后：只用于建立映射)
  private static async getAllItems(): Promise<BoxHeroItem[]> {
    try {
      const response = await this.request<any>('/items/');
      console.log('BoxHero getAllItems - 响应类型:', typeof response);
      console.log('BoxHero getAllItems - 是否为数组:', Array.isArray(response));
      
      // 处理不同的响应格式，根据单个item的响应格式，items列表可能也有类似结构
      let items: any[] = [];
      if (Array.isArray(response)) {
        items = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        items = response.data;
        console.log('BoxHero getAllItems - 从data字段获取数组');
      } else if (response && response.items && Array.isArray(response.items)) {
        items = response.items;
        console.log('BoxHero getAllItems - 从items字段获取数组');
      } else {
        console.warn('BoxHero getAllItems - 无法解析响应格式:', response);
        return [];
      }
      
      console.log('BoxHero getAllItems - 解析出的items数量:', items.length);
      if (items.length > 0) {
        console.log('BoxHero getAllItems - 第一个item结构:', JSON.stringify(items[0], null, 2));
      }
      
      // 转换为标准格式 - 注意：getAllItems的响应格式可能与getItemById不同
      const normalizedItems: BoxHeroItem[] = items.map((item: any) => ({
        id: (item.id || item._id || '')?.toString(),
        name: item.name || item.title || '',
        barcode: item.barcode || item.code || item.sku || '',
        quantity: item.quantity || item.stock || item.amount || 0,
        unit: item.unit || '',
        location: item.location || '',
        description: item.description || item.desc || '',
        tags: item.tags || [],
        createdAt: item.createdAt || item.created_at || '',
        updatedAt: item.updatedAt || item.updated_at || ''
      }));
      
      console.log('BoxHero getAllItems - 标准化后的items数量:', normalizedItems.length);
      return normalizedItems;
    } catch (error) {
      console.error('获取BoxHero库存失败:', error);
      return [];
    }
  }

  // 通过item_id获取特定item (新的优化方法)
  static async getItemById(itemId: string): Promise<BoxHeroItem | null> {
    try {
      console.log(`获取BoxHero item详情 - ID: ${itemId}`);
      const response = await this.request<any>(`/items/${itemId}`);
      
      if (!response || !response.item) {
        console.log(`BoxHero item未找到 - ID: ${itemId}`);
        return null;
      }

      const item = response.item;
      console.log('BoxHero getItemById - item数据:', JSON.stringify(item, null, 2));
      
      // 标准化item数据 - 注意BoxHero API响应格式为 { "item": { ... } }
      const normalizedItem: BoxHeroItem = {
        id: item.id?.toString() || '',
        name: item.name || '',
        barcode: item.barcode || item.sku || '',
        quantity: item.quantity || 0,
        unit: item.unit || '',
        location: item.location || '',
        description: item.description || '',
        tags: item.tags || [],
        createdAt: item.createdAt || item.created_at || '',
        updatedAt: item.updatedAt || item.updated_at || ''
      };
      
      console.log(`BoxHero getItemById成功 - ID: ${itemId}, 库存: ${normalizedItem.quantity}`);
      return normalizedItem;
      
    } catch (error) {
      console.error(`获取BoxHero item失败 - ID: ${itemId}:`, error);
      return null;
    }
  }

  // 根据barcode查找item (优化后：使用特定item端点)
  static async getItemByBarcode(barcode: string): Promise<BoxHeroItem | null> {
    try {
      console.log(`查找BoxHero item - 条码: ${barcode}`);
      
      // 获取item_id
      const itemId = await this.getItemIdByBarcode(barcode);
      if (!itemId) {
        console.log(`条码未找到对应的item ID: ${barcode}`);
        return null;
      }
      
      // 使用特定端点获取item详情
      const item = await this.getItemById(itemId);
      console.log(`条码 ${barcode} 查找结果:`, item ? `找到，库存: ${item.quantity}` : '未找到');
      
      return item;
    } catch (error) {
      console.error('根据条码查找BoxHero库存失败:', error);
      return null;
    }
  }

  // 批量获取多个barcode的库存 (优化后：使用特定item端点)
  static async getItemsByBarcodes(barcodes: string[]): Promise<Map<string, BoxHeroItem>> {
    try {
      console.log(`批量获取BoxHero库存 - 条码数量: ${barcodes.length}`);
      const itemsMap = new Map<string, BoxHeroItem>();
      
      // 建立映射
      await this.buildBarcodeMapping();
      
      // 收集所有有效的item IDs
      const itemIds: string[] = [];
      const barcodeToIdMapping = new Map<string, string>();
      
      barcodes.forEach(barcode => {
        const itemId = this.barcodeToIdMap.get(barcode);
        if (itemId) {
          itemIds.push(itemId);
          barcodeToIdMapping.set(itemId, barcode);
        }
      });
      
      console.log(`找到 ${itemIds.length} 个有效的item IDs`);
      
      // 并发获取所有item详情（但受速率限制器控制）
      const itemPromises = itemIds.map(itemId => 
        this.getItemById(itemId).then(item => ({ itemId, item }))
      );
      
      const results = await Promise.all(itemPromises);
      
      // 构建结果映射
      results.forEach(({ itemId, item }) => {
        if (item) {
          const barcode = barcodeToIdMapping.get(itemId);
          if (barcode) {
            itemsMap.set(barcode, item);
          }
        }
      });
      
      console.log(`批量获取完成 - 成功获取 ${itemsMap.size} 个库存信息`);
      return itemsMap;
      
    } catch (error) {
      console.error('批量获取BoxHero库存失败:', error);
      return new Map();
    }
  }

  // 清除映射缓存（用于强制刷新）
  static clearMappingCache(): void {
    this.barcodeToIdMap.clear();
    this.idToBarcodeMap.clear();
    this.mappingCacheTimestamp = 0;
    console.log('BoxHero映射缓存已清除');
  }

  // 获取映射缓存状态
  static getMappingCacheInfo(): {
    size: number;
    age: number;
    isExpired: boolean;
  } {
    const now = Date.now();
    const age = this.mappingCacheTimestamp > 0 ? now - this.mappingCacheTimestamp : 0;
    const isExpired = age >= this.MAPPING_CACHE_DURATION;
    
    return {
      size: this.barcodeToIdMap.size,
      age: Math.round(age / 1000), // 返回秒数
      isExpired
    };
  }
}