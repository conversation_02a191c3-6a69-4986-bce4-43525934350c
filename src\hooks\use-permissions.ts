'use client';

import { useState, useEffect } from 'react';
import { ApiService } from '@/lib/api';
import { Operator } from '@/lib/types';

interface UsePermissionsReturn {
  operator: Operator | null;
  isLoading: boolean;
  error: string | null;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  isAdmin: boolean;
  canAccessInventoryCount: boolean;
  canManageInventoryCount: boolean;
}

export function usePermissions(): UsePermissionsReturn {
  const [operator, setOperator] = useState<Operator | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCurrentOperator();
  }, []);

  const loadCurrentOperator = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await ApiService.getCurrentOperator();
      
      if (response.result) {
        setOperator(response.result);
      } else {
        setError('无法获取用户信息');
      }
    } catch (err) {
      console.error('获取用户信息失败:', err);
      setError(err instanceof Error ? err.message : '获取用户信息失败');
    } finally {
      setIsLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!operator) return false;
    return operator.permissions?.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    if (!operator) return false;
    return operator.roles?.includes(role) || false;
  };

  const isAdmin = hasRole('admin') || hasRole('administrator') || operator?.id === 2237;

  const canAccessInventoryCount =
    isAdmin ||
    hasPermission('inventory_count') ||
    hasPermission('inventory_count_access') ||
    operator?.id === 2237;

  const canManageInventoryCount =
    isAdmin ||
    hasPermission('inventory_count_manage') ||
    hasPermission('inventory_count_admin') ||
    operator?.id === 2237;

  return {
    operator,
    isLoading,
    error,
    hasPermission,
    hasRole,
    isAdmin,
    canAccessInventoryCount,
    canManageInventoryCount,
  };
}
