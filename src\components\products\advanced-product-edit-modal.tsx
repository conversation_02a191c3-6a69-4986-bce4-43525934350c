'use client';

import { useState, useEffect } from 'react';
import { Product, ProductGroup, UserGroup } from '@/lib/types';
import { ApiService } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface AdvancedProductEditModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

interface GeneralFormData {
  productType: number;
  productGroupId: number | undefined;
  name: string;
  description: string;
  price: number;
  cost: number;
  disallowClientOrder: boolean;
  restrictGuestSale: boolean;
  restrictSale: boolean;
  purchaseOptions: number;
  points: number;
  pointsPrice: number;
  barcode: string;
  enableStock: boolean;
  disallowSaleIfOutOfStock: boolean;
  stockAlert: boolean;
  stockAlertThreshold: number;
  stockTargetDifferentProduct: boolean;
  stockTargetProductId: number;
  stockProductAmount: number;
  isDeleted: boolean;
  isService: boolean;
  displayOrder: number;
}

interface UserPriceData {
  userGroupId: number;
  price: number;
  pointsPrice: number;
  purchaseOptions: number;
  isEnabled: boolean;
  isPointsPriceManuallyModified?: boolean; // Track if pointsPrice was manually modified
}

interface RestrictionData {
  userGroupId: number;
  isDisallowed: boolean;
}

export function AdvancedProductEditModal({ product, isOpen, onClose, onUpdate }: AdvancedProductEditModalProps) {
  const [activeTab, setActiveTab] = useState('general');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);

  // General form data
  const [generalData, setGeneralData] = useState<GeneralFormData>({
    productType: product.productType || 0,
    productGroupId: product.productGroupId && product.productGroupId > 0 ? product.productGroupId : undefined,
    name: product.name,
    description: product.description || '',
    price: product.price,
    cost: product.cost || 0,
    disallowClientOrder: product.disallowClientOrder || false,
    restrictGuestSale: product.restrictGuestSale || false,
    restrictSale: product.restrictSale || false,
    purchaseOptions: product.purchaseOptions || 0,
    points: product.points || 0,
    pointsPrice: product.pointsPrice || 0,
    barcode: product.barcode || '',
    enableStock: product.enableStock || true,
    disallowSaleIfOutOfStock: product.disallowSaleIfOutOfStock || true,
    stockAlert: product.stockAlert || true,
    stockAlertThreshold: product.stockAlertThreshold || 0,
    stockTargetDifferentProduct: product.stockTargetDifferentProduct || false,
    stockTargetProductId: product.stockTargetProductId || 0,
    stockProductAmount: product.stockProductAmount || 0,
    isDeleted: product.isDeleted || false,
    isService: product.isService || false,
    displayOrder: product.displayOrder || 0,
  });

  // User prices data
  const [userPrices, setUserPrices] = useState<UserPriceData[]>([]);

  // Restrictions data
  const [restrictions, setRestrictions] = useState<RestrictionData[]>([]);

  // Loading state to prevent duplicate data loading
  const [isLoadingData, setIsLoadingData] = useState(false);

  useEffect(() => {
    if (isOpen && !isLoadingData) {
      loadInitialData();
    }
  }, [isOpen, product.id]);

  const loadInitialData = async () => {
    if (isLoadingData) return; // Prevent duplicate loading

    try {
      setIsLoadingData(true);


      // Load product groups
      const groupsResponse = await ApiService.fetchProductGroups();
      console.log('Product groups response:', groupsResponse);

      // 处理可能的嵌套数据结构
      let productGroupsData = [];
      if (groupsResponse.result) {
        if (Array.isArray(groupsResponse.result)) {
          productGroupsData = groupsResponse.result;
        } else if (groupsResponse.result.data && Array.isArray(groupsResponse.result.data)) {
          productGroupsData = groupsResponse.result.data;
        } else if (groupsResponse.result.items && Array.isArray(groupsResponse.result.items)) {
          productGroupsData = groupsResponse.result.items;
        }
      }
      setProductGroups(productGroupsData);

      // Load user groups
      const userGroupsResponse = await ApiService.fetchUserGroups();
      console.log('User groups response:', userGroupsResponse);

      // 处理可能的嵌套数据结构
      let userGroupsData = [];
      if (userGroupsResponse.result) {
        if (Array.isArray(userGroupsResponse.result)) {
          userGroupsData = userGroupsResponse.result;
        } else if (userGroupsResponse.result.data && Array.isArray(userGroupsResponse.result.data)) {
          userGroupsData = userGroupsResponse.result.data;
        } else if (userGroupsResponse.result.items && Array.isArray(userGroupsResponse.result.items)) {
          userGroupsData = userGroupsResponse.result.items;
        }
      }

      if (userGroupsData.length > 0) {
        setUserGroups(userGroupsData);

        // Initialize user prices for all user groups
        const initialUserPrices = userGroupsData.map((group: UserGroup) => ({
          userGroupId: group.id,
          price: product.price, // Default to general price
          pointsPrice: product.pointsPrice || 0, // Default to general pointsPrice
          purchaseOptions: 0,
          isEnabled: false, // Default to disabled, only enable if API data exists
          isPointsPriceManuallyModified: false, // Initially not manually modified
        }));
        setUserPrices(initialUserPrices);

        // Initialize restrictions for all user groups
        const initialRestrictions = userGroupsData.map((group: UserGroup) => ({
          userGroupId: group.id,
          isDisallowed: false,
        }));
        setRestrictions(initialRestrictions);
      }

      // Load existing user prices from API
      try {
        const userPricesResponse = await ApiService.fetchProductUserPrices(product.id);
        console.log('User prices response:', userPricesResponse);

        if (userPricesResponse.result && Array.isArray(userPricesResponse.result)) {
          const existingPrices = userPricesResponse.result.map((up: any) => {
            // Handle different possible field names for points price
            const apiPointsPrice = up.pointsPrice ?? up.pointPrice ?? up.points;

            return {
              userGroupId: up.userGroupId,
              price: up.price,
              pointsPrice: apiPointsPrice, // Keep null if API returns null, don't convert to 0
              purchaseOptions: up.purchaseOptions || 0,
              isEnabled: up.isEnabled === true, // Only enable if explicitly true
              isPointsPriceManuallyModified: apiPointsPrice != null, // Mark as manually modified only if API has a non-null value
            };
          });

          setUserPrices(prev => {
            const updated = prev.map(p => {
              const existing = existingPrices.find(ep => ep.userGroupId === p.userGroupId);
              if (existing) {
                // Merge existing data with initial data to preserve all fields
                const merged = {
                  ...p, // Keep initial data as base
                  ...existing, // Override with existing data
                  price: existing.price || p.price, // Ensure price is not null/undefined
                  pointsPrice: existing.pointsPrice != null ? existing.pointsPrice : p.pointsPrice, // Only use API value if it's not null
                };

                return merged;
              }

              return p;
            });

            return updated;
          });
        }
      } catch (error) {
        console.error('Failed to load user prices:', error);
      }

      // Load existing restrictions from API
      try {
        const restrictionsResponse = await ApiService.fetchProductDisallowedUserGroups(product.id);
        console.log('Restrictions response:', restrictionsResponse);

        if (restrictionsResponse.result && Array.isArray(restrictionsResponse.result)) {
          setRestrictions(prev =>
            prev.map(r => {
              const existing = restrictionsResponse.result.find((dug: any) => dug.userGroupId === r.userGroupId);
              return existing ? { ...r, isDisallowed: existing.isDisallowed === true } : r;
            })
          );
        }
      } catch (error) {
        console.error('Failed to load restrictions:', error);
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      setError('加载数据失败');
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleGeneralChange = (field: keyof GeneralFormData, value: any) => {
    setGeneralData(prev => ({ ...prev, [field]: value }));

    // When general price changes, update all user prices to match
    if (field === 'price') {
      setUserPrices(prev => prev.map(up => ({ ...up, price: value })));
    }

    // When general pointsPrice changes, update pointsPrice for user groups that haven't been manually modified
    if (field === 'pointsPrice') {
      setUserPrices(prev => prev.map(up =>
        !up.isPointsPriceManuallyModified ? { ...up, pointsPrice: value } : up
      ));
    }



    // When general purchaseOptions changes, update all user prices to match
    if (field === 'purchaseOptions') {
      setUserPrices(prev => prev.map(up => ({ ...up, purchaseOptions: value })));
    }
  };

  const handleUserPriceChange = (userGroupId: number, field: keyof UserPriceData, value: any) => {
    setUserPrices(prev => prev.map(up => {
      if (up.userGroupId === userGroupId) {
        const updatedPrice = { ...up, [field]: value };
        // Mark pointsPrice as manually modified when user changes it
        if (field === 'pointsPrice') {
          updatedPrice.isPointsPriceManuallyModified = true;
        }
        return updatedPrice;
      }
      return up;
    }));
  };

  const handleRestrictionChange = (userGroupId: number, isDisallowed: boolean) => {
    setRestrictions(prev => prev.map(r => 
      r.userGroupId === userGroupId ? { ...r, isDisallowed } : r
    ));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 准备产品数据 - 过滤掉无效值
      const productData: any = {
        id: product.id,
        name: generalData.name,
        description: generalData.description || '',
        price: generalData.price,
        cost: generalData.cost || 0,
        barcode: generalData.barcode || '',
      };

      // 只有当productGroupId有效时才包含它
      if (generalData.productGroupId && generalData.productGroupId > 0) {
        productData.productGroupId = generalData.productGroupId;
      }

      // 只有当productType有效时才包含它
      if (generalData.productType !== undefined && generalData.productType !== null) {
        productData.productType = generalData.productType;
      }

      // 添加其他可选字段（只有当它们有有效值时）
      if (generalData.purchaseOptions !== undefined && generalData.purchaseOptions !== null) {
        productData.purchaseOptions = generalData.purchaseOptions;
      }

      if (generalData.points !== undefined && generalData.points !== null) {
        productData.points = generalData.points;
      }

      if (generalData.pointsPrice !== undefined && generalData.pointsPrice !== null) {
        productData.pointsPrice = generalData.pointsPrice;
      }

      console.log('准备更新的产品数据:', productData);
      console.log('原始表单数据:', generalData);

      console.log('原始产品数据:', product);
      console.log('表单数据:', generalData);

      // 1. Update general product data (使用PUT方法更新现有产品)
      console.log('开始更新产品基本信息...');
      const updateResponse = await ApiService.updateProduct(productData);
      console.log('产品更新响应:', updateResponse);

      // 2. Update user prices - 重新加载最新的用户价格数据
      console.log('开始处理用户价格...');

      // 先获取最新的用户价格数据
      let currentUserPrices = [];
      try {
        const userPricesResponse = await ApiService.fetchProductUserPrices(product.id);
        if (userPricesResponse.result && Array.isArray(userPricesResponse.result)) {
          currentUserPrices = userPricesResponse.result;
          console.log('当前用户价格数据:', currentUserPrices);
        }
      } catch (error) {
        console.error('获取用户价格失败:', error);
      }

      for (const userPrice of userPrices) {
        if (userPrice.isEnabled) {
          console.log(`处理用户组 ${userPrice.userGroupId} 的价格...`);

          // 检查是否已存在该用户组的价格设置
          const existingUserPrice = currentUserPrices.find((up: any) => up.userGroupId === userPrice.userGroupId);
          console.log(`用户组 ${userPrice.userGroupId} 的现有价格:`, existingUserPrice);

          if (existingUserPrice) {
            console.log(`更新现有用户价格，ID: ${existingUserPrice.id}`);
            // 更新现有价格
            await ApiService.updateProductUserPrice({
              id: existingUserPrice.id,
              productId: product.id, // 添加产品ID
              userGroupId: userPrice.userGroupId,
              userGroupName: existingUserPrice.userGroupName || '',
              price: userPrice.price,
              pointsPrice: userPrice.pointsPrice,
              purchaseOptions: userPrice.purchaseOptions,
              isEnabled: userPrice.isEnabled,
            });
          } else {
            console.log(`创建新用户价格，用户组: ${userPrice.userGroupId}`);
            // 创建新价格
            await ApiService.createProductUserPrice(product.id, {
              userGroupId: userPrice.userGroupId,
              price: userPrice.price,
              pointsPrice: userPrice.pointsPrice,
              purchaseOptions: userPrice.purchaseOptions,
              isEnabled: userPrice.isEnabled,
            });
          }
        } else {
          // 如果用户价格被禁用，检查是否需要删除现有的价格设置
          const existingUserPrice = currentUserPrices.find((up: any) => up.userGroupId === userPrice.userGroupId);
          if (existingUserPrice) {
            console.log(`删除用户组 ${userPrice.userGroupId} 的价格设置`);
            try {
              await ApiService.deleteProductUserPrice(product.id, existingUserPrice.id);
            } catch (error) {
              console.error(`删除用户价格失败:`, error);
              // 删除失败不阻止整个保存过程
            }
          }
        }
      }

      // 3. Update restrictions
      console.log('开始处理访问限制...');

      // 先获取最新的限制数据
      let currentRestrictions = [];
      try {
        const restrictionsResponse = await ApiService.fetchProductDisallowedUserGroups(product.id);
        if (restrictionsResponse.result && Array.isArray(restrictionsResponse.result)) {
          currentRestrictions = restrictionsResponse.result;
          console.log('当前限制数据:', currentRestrictions);
        }
      } catch (error) {
        console.error('获取限制数据失败:', error);
      }

      for (const restriction of restrictions) {
        console.log(`处理用户组 ${restriction.userGroupId} 的限制...`);

        const existingRestriction = currentRestrictions.find((r: any) => r.userGroupId === restriction.userGroupId);

        if (restriction.isDisallowed) {
          if (existingRestriction) {
            console.log(`更新现有限制，ID: ${existingRestriction.id}`);
            await ApiService.updateDisallowedUserGroup({
              id: existingRestriction.id,
              productId: product.id,
              userGroupId: restriction.userGroupId,
              isDisallowed: restriction.isDisallowed,
            });
          } else {
            console.log(`创建新限制，用户组: ${restriction.userGroupId}`);
            await ApiService.createDisallowedUserGroup(product.id, {
              userGroupId: restriction.userGroupId,
              isDisallowed: restriction.isDisallowed,
            });
          }
        } else if (existingRestriction) {
          // 如果限制被取消，删除现有的限制
          console.log(`删除用户组 ${restriction.userGroupId} 的限制`);
          try {
            await ApiService.deleteDisallowedUserGroup(product.id, existingRestriction.id);
          } catch (error) {
            console.error(`删除限制失败:`, error);
            // 删除失败不阻止整个保存过程
          }
        }
      }

      onUpdate();
      onClose();
    } catch (err) {
      console.error('保存失败:', err);
      let errorMessage = '保存失败';

      if (err instanceof Error) {
        errorMessage = err.message;
        console.error('错误详情:', err);
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>高级产品编辑 - {product.name}</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general" className="text-sm font-medium">通用设置</TabsTrigger>
            <TabsTrigger value="pricing" className="text-sm font-medium">用户组价格</TabsTrigger>
            <TabsTrigger value="restrictions" className="text-sm font-medium">访问限制</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">产品名称</label>
                <Input
                  value={generalData.name}
                  onChange={(e) => handleGeneralChange('name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">产品组</label>
                <select
                  value={generalData.productGroupId ? generalData.productGroupId.toString() : ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    handleGeneralChange('productGroupId', value ? parseInt(value) : undefined);
                  }}
                  className="flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-black ring-offset-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">请选择产品组</option>
                  {productGroups.map(group => (
                    <option key={group.id} value={group.id}>{group.name}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">产品描述</label>
              <Textarea
                value={generalData.description}
                onChange={(e) => handleGeneralChange('description', e.target.value)}
                rows={3}
              />
            </div>

            {/* 价格和成本 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">价格 (RM)</label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">RM</span>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={generalData.price}
                    onChange={(e) => handleGeneralChange('price', parseFloat(e.target.value) || 0)}
                    className="text-base font-medium"
                  />
                  <div className="flex items-center space-x-2">
                    <select
                      value={generalData.purchaseOptions}
                      onChange={(e) => handleGeneralChange('purchaseOptions', parseInt(e.target.value))}
                      className="px-2 py-1 border border-gray-300 rounded text-sm bg-blue-500 text-white"
                    >
                      <option value={1}>Or</option>
                      <option value={0}>And</option>
                    </select>
                    <Input
                      type="number"
                      min="0"
                      value={generalData.pointsPrice}
                      onChange={(e) => handleGeneralChange('pointsPrice', parseInt(e.target.value) || 0)}
                      className="w-20 text-base font-medium"
                      placeholder="积分"
                    />
                    <span className="text-sm text-gray-600">Points</span>
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">成本 (RM)</label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">RM</span>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={generalData.cost}
                    onChange={(e) => handleGeneralChange('cost', parseFloat(e.target.value) || 0)}
                    className="text-base font-medium"
                  />
                </div>
              </div>
            </div>

            {/* 奖励积分和条码 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">奖励积分</label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    min="0"
                    value={generalData.points}
                    onChange={(e) => handleGeneralChange('points', parseInt(e.target.value) || 0)}
                    className="text-base font-medium"
                  />
                  <span className="text-sm text-gray-600">Points</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">条码</label>
                <Input
                  value={generalData.barcode}
                  onChange={(e) => handleGeneralChange('barcode', e.target.value)}
                  className="text-base font-medium"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.enableStock}
                    onChange={(e) => handleGeneralChange('enableStock', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">启用库存管理</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.disallowSaleIfOutOfStock}
                    onChange={(e) => handleGeneralChange('disallowSaleIfOutOfStock', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">缺货时禁止销售</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.stockAlert}
                    onChange={(e) => handleGeneralChange('stockAlert', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">库存警报</label>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.restrictSale}
                    onChange={(e) => handleGeneralChange('restrictSale', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">限制销售</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.restrictGuestSale}
                    onChange={(e) => handleGeneralChange('restrictGuestSale', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">限制访客购买</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.isService}
                    onChange={(e) => handleGeneralChange('isService', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">服务类产品</label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">用户组价格设置</h3>
              <p className="text-sm text-gray-700 font-medium">
                勾选ENABLE来启用特定用户组的价格设置。启用后价格会自动与通用价格同步。
              </p>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-blue-100">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">ENABLE</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">USER GROUP</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">PRICE</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">OPTIONS</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">POINTS PRICE</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userGroups.map(group => {
                      const userPrice = userPrices.find(up => up.userGroupId === group.id);
                      if (!userPrice) return null;

                      return (
                        <tr key={group.id} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-3 py-2">
                            <Checkbox
                              checked={userPrice.isEnabled}
                              onChange={(e) => handleUserPriceChange(group.id, 'isEnabled', e.target.checked)}
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-sm font-semibold text-gray-900">
                            {group.name}
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <div className="flex items-center space-x-1">
                              <span className="text-xs text-gray-600">RM</span>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                value={userPrice.price}
                                onChange={(e) => handleUserPriceChange(group.id, 'price', parseFloat(e.target.value) || 0)}
                                className="w-20 text-sm font-medium"
                                disabled={!userPrice.isEnabled}
                              />
                            </div>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <select
                              value={userPrice.purchaseOptions}
                              onChange={(e) => handleUserPriceChange(group.id, 'purchaseOptions', parseInt(e.target.value))}
                              className="px-2 py-1 border border-gray-300 rounded text-xs bg-blue-500 text-white font-medium"
                              disabled={!userPrice.isEnabled}
                            >
                              <option value={1}>Or</option>
                              <option value={0}>And</option>
                            </select>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              type="number"
                              min="0"
                              value={userPrice.pointsPrice}
                              onChange={(e) => handleUserPriceChange(group.id, 'pointsPrice', parseInt(e.target.value) || 0)}
                              className="w-20 text-sm font-medium"
                              disabled={!userPrice.isEnabled}
                            />
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="restrictions" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">用户组访问限制</h3>
              <p className="text-sm text-gray-700 font-medium">
                勾选DISALLOWED来禁止特定用户组访问此产品。
              </p>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-blue-100">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">NAME</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">DISALLOWED</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userGroups.map(group => {
                      const restriction = restrictions.find(r => r.userGroupId === group.id);
                      if (!restriction) return null;

                      return (
                        <tr key={group.id} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-3 py-2 text-sm font-semibold text-gray-900">
                            {group.name}
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Checkbox
                              checked={restriction.isDisallowed}
                              onChange={(e) => handleRestrictionChange(group.id, e.target.checked)}
                            />
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? '保存中...' : '保存所有更改'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
