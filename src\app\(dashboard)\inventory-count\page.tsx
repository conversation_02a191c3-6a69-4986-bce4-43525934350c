'use client';

import { useState, useEffect } from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { ApiService } from '@/lib/api';
import { InventoryCountItem, InventoryCountSession, ProductGroup } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { LoadingIndicator } from '@/components/ui/loading';

export default function InventoryCountPage() {
  const { canAccessInventoryCount, isLoading: permissionsLoading } = usePermissions();
  const [session, setSession] = useState<InventoryCountSession | null>(null);
  const [items, setItems] = useState<InventoryCountItem[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    if (!permissionsLoading && canAccessInventoryCount) {
      initializeSession();
    }
  }, [permissionsLoading, canAccessInventoryCount]);

  useEffect(() => {
    if (session) {
      loadItems();
    }
  }, [session, selectedGroupId]);

  const initializeSession = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 检查是否有活跃的盘点会话
      const activeSessionResponse = await ApiService.getActiveInventoryCountSession();
      
      let currentSession: InventoryCountSession;
      
      if (activeSessionResponse.result) {
        currentSession = activeSessionResponse.result;
      } else {
        // 创建新的盘点会话
        const newSessionResponse = await ApiService.createInventoryCountSession();
        if (!newSessionResponse.result) {
          throw new Error('无法创建盘点会话');
        }
        currentSession = newSessionResponse.result;
      }

      setSession(currentSession);

      // 加载产品组
      const productGroupsResponse = await ApiService.fetchProductGroups();
      if (productGroupsResponse.result) {
        console.log('产品组API响应:', productGroupsResponse.result);
        let productGroupsData = [];
        if (Array.isArray(productGroupsResponse.result)) {
          productGroupsData = productGroupsResponse.result;
        } else if (productGroupsResponse.result && typeof productGroupsResponse.result === 'object') {
          // 处理可能的嵌套数据结构
          const result = productGroupsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            productGroupsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productGroupsData = result.items;
          }
        }
        setProductGroups(productGroupsData);
      }
    } catch (err) {
      console.error('初始化盘点会话失败:', err);
      setError(err instanceof Error ? err.message : '初始化盘点会话失败');
    } finally {
      setIsLoading(false);
    }
  };

  const loadItems = async () => {
    if (!session) return;

    try {
      setError(null);
      const response = await ApiService.getInventoryCountItems(session.id, selectedGroupId || undefined);
      
      if (response.result) {
        setItems(Array.isArray(response.result) ? response.result : []);
      }
    } catch (err) {
      console.error('加载盘点项目失败:', err);
      setError(err instanceof Error ? err.message : '加载盘点项目失败');
    }
  };

  const handleCountChange = (itemId: number, countedStock: string) => {
    const numericValue = countedStock === '' ? null : parseInt(countedStock, 10);
    
    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const newCountedStock = numericValue;
        const difference = newCountedStock !== null ? item.systemStock - newCountedStock : 0;
        
        return {
          ...item,
          countedStock: newCountedStock,
          difference,
          status: 'draft' as const
        };
      }
      return item;
    }));
  };

  const handleSave = async () => {
    if (!session) return;

    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      const itemsToSave = items.filter(item => item.countedStock !== null);
      
      for (const item of itemsToSave) {
        await ApiService.saveInventoryCountItem({
          ...item,
          countSessionId: session.id,
          status: 'draft'
        });
      }

      setSuccessMessage(`已保存 ${itemsToSave.length} 个产品的盘点数据`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error('保存盘点数据失败:', err);
      setError(err instanceof Error ? err.message : '保存盘点数据失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async () => {
    if (!session) return;

    const unCountedItems = items.filter(item => item.countedStock === null);
    
    if (unCountedItems.length > 0) {
      const confirmed = window.confirm(
        `还有 ${unCountedItems.length} 个产品未盘点，确定要提交吗？未盘点的产品将被标记为0。`
      );
      
      if (!confirmed) return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccessMessage(null);

      // 先保存所有数据
      for (const item of items) {
        await ApiService.saveInventoryCountItem({
          ...item,
          countSessionId: session.id,
          countedStock: item.countedStock ?? 0,
          difference: (item.countedStock ?? 0) - item.systemStock,
          status: 'submitted'
        });
      }

      // 提交会话
      await ApiService.submitInventoryCountSession(session.id);

      setSuccessMessage('盘点数据已成功提交！');
      
      // 重新初始化会话
      setTimeout(() => {
        initializeSession();
      }, 2000);
    } catch (err) {
      console.error('提交盘点数据失败:', err);
      setError(err instanceof Error ? err.message : '提交盘点数据失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getItemBackgroundColor = (item: InventoryCountItem) => {
    if (item.countedStock === null) return '';
    
    if (item.countedStock === item.systemStock) {
      return 'bg-green-50 border-green-200';
    } else {
      return 'bg-red-50 border-red-200';
    }
  };

  const getCompletionStats = () => {
    const total = items.length;
    const completed = items.filter(item => item.countedStock !== null).length;
    const discrepancies = items.filter(item => 
      item.countedStock !== null && item.countedStock !== item.systemStock
    ).length;
    
    return { total, completed, discrepancies };
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="检查权限中..." />
      </div>
    );
  }

  if (!canAccessInventoryCount) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-6xl mb-4">🚫</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">访问被拒绝</h3>
        <p className="text-gray-500">您没有权限访问库存盘点页面</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="初始化盘点会话中..." />
      </div>
    );
  }

  const stats = getCompletionStats();

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">库存盘点</h1>
              <p className="text-gray-600 mt-1">
                会话ID: {session?.id} | 进度: {stats.completed}/{stats.total} | 差异: {stats.discrepancies}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={handleSave}
                disabled={isSaving || isSubmitting}
                variant="outline"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    保存中...
                  </>
                ) : (
                  <>
                    <span className="mr-2">💾</span>
                    保存
                  </>
                )}
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSaving || isSubmitting}
                variant="primary"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    提交中...
                  </>
                ) : (
                  <>
                    <span className="mr-2">✅</span>
                    提交
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <div className="text-red-600 text-xl mr-3">❌</div>
                <div>
                  <h3 className="text-red-800 font-medium">错误</h3>
                  <p className="text-red-700 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <div className="text-green-600 text-xl mr-3">✅</div>
                <div>
                  <h3 className="text-green-800 font-medium">成功</h3>
                  <p className="text-green-700 mt-1">{successMessage}</p>
                </div>
              </div>
            </div>
          )}

          {/* 产品组选择标签 */}
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedGroupId(null)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedGroupId === null
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                全部
              </button>
              {productGroups.map(group => (
                <button
                  key={group.id}
                  onClick={() => setSelectedGroupId(group.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedGroupId === group.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {group.name}
                </button>
              ))}
            </div>
          </div>

          {/* 产品列表 */}
          <div className="space-y-3">
            {items.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📦</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到产品</h3>
                <p className="text-gray-500">请选择产品组或检查盘点配置</p>
              </div>
            ) : (
              items.map(item => (
                <div
                  key={item.id || `${item.productId}-${item.countSessionId}`}
                  className={`border rounded-lg p-4 transition-colors ${getItemBackgroundColor(item)}`}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item.productName}</h3>
                      <p className="text-sm text-gray-600">
                        {item.productGroupName} | 系统库存: {item.systemStock}
                        {item.countedStock !== null && (
                          <span className={`ml-2 font-medium ${
                            item.difference === 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            | 差异: {item.difference > 0 ? '+' : ''}{item.difference}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <label className="block text-xs text-gray-500 mb-1">盘点数量</label>
                        <input
                          type="number"
                          min="0"
                          value={item.countedStock ?? ''}
                          onChange={(e) => handleCountChange(item.id!, e.target.value)}
                          className="w-24 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="0"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
