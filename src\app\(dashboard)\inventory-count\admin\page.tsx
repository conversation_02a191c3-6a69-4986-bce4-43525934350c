'use client';

import { useState, useEffect } from 'react';
import { usePermissions } from '@/hooks/use-permissions';
import { ApiService } from '@/lib/api';
import { InventoryCountConfig, InventoryCountConfigProduct, Operator } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { LoadingIndicator } from '@/components/ui/loading';
import { ProductGroupSelector } from '@/components/inventory-count/product-group-selector';

export default function InventoryCountAdminPage() {
  const { canManageInventoryCount, isLoading: permissionsLoading } = usePermissions();
  const [config, setConfig] = useState<InventoryCountConfig>({
    configName: 'Default Config',
    authorizedOperators: [],
    frequency: 'daily',
    scheduleTime: '09:00',
    scheduleDays: [],
    isActive: false,
    createdBy: 2237,
  });

  const [selectedProducts, setSelectedProducts] = useState<InventoryCountConfigProduct[]>([]);
  const [operators, setOperators] = useState<Operator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    if (!permissionsLoading && canManageInventoryCount) {
      loadData();
    }
  }, [permissionsLoading, canManageInventoryCount]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [configResponse, operatorsResponse] = await Promise.all([
        ApiService.getInventoryCountConfig(),
        ApiService.fetchOperators(),
      ]);

      if (configResponse.result) {
        setConfig(configResponse.result);
        // 如果配置中有产品数据，也加载它们
        if (configResponse.result.id) {
          const productsResponse = await ApiService.getInventoryCountConfigProducts(configResponse.result.id);
          if (productsResponse.result) {
            setSelectedProducts(productsResponse.result);
          }
        }
      }

      if (operatorsResponse.result) {
        console.log('操作员API响应:', operatorsResponse.result);
        let operatorsData = [];
        if (Array.isArray(operatorsResponse.result)) {
          operatorsData = operatorsResponse.result;
        } else if (operatorsResponse.result && typeof operatorsResponse.result === 'object') {
          // 处理可能的嵌套数据结构
          const result = operatorsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            operatorsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            operatorsData = result.items;
          }
        }
        setOperators(operatorsData);
      }


    } catch (err) {
      console.error('加载数据失败:', err);
      setError(err instanceof Error ? err.message : '加载数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveConfig = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      // 保存基本配置
      const configResponse = await ApiService.saveInventoryCountConfig(config);

      if (configResponse.result) {
        const savedConfig = configResponse.result;
        setConfig(savedConfig);

        // 保存选中的产品
        if (savedConfig.id && selectedProducts.length > 0) {
          const productsToSave = selectedProducts.map(product => ({
            ...product,
            configId: savedConfig.id!,
          }));

          await ApiService.saveInventoryCountConfigProducts(productsToSave);
        }

        setSuccessMessage(`配置保存成功！已选择 ${selectedProducts.length} 个产品进行盘点。`);
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (err) {
      console.error('保存配置失败:', err);
      setError(err instanceof Error ? err.message : '保存配置失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleOperatorToggle = (operatorId: number) => {
    setConfig(prev => ({
      ...prev,
      authorizedOperators: prev.authorizedOperators.includes(operatorId)
        ? prev.authorizedOperators.filter(id => id !== operatorId)
        : [...prev.authorizedOperators, operatorId]
    }));
  };



  const handleScheduleDayToggle = (day: number) => {
    setConfig(prev => ({
      ...prev,
      scheduleDays: prev.scheduleDays?.includes(day)
        ? prev.scheduleDays.filter(d => d !== day)
        : [...(prev.scheduleDays || []), day]
    }));
  };

  if (permissionsLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="检查权限中..." />
      </div>
    );
  }

  if (!canManageInventoryCount) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-6xl mb-4">🚫</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">访问被拒绝</h3>
        <p className="text-gray-500">您没有权限访问库存盘点管理页面</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="加载配置中..." />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">库存盘点管理配置</h1>
          <p className="text-gray-600 mt-1">配置库存盘点的权限、范围和频率设置</p>
        </div>

        <div className="p-6 space-y-8">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="text-red-600 text-xl mr-3">❌</div>
                <div>
                  <h3 className="text-red-800 font-medium">错误</h3>
                  <p className="text-red-700 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex">
                <div className="text-green-600 text-xl mr-3">✅</div>
                <div>
                  <h3 className="text-green-800 font-medium">成功</h3>
                  <p className="text-green-700 mt-1">{successMessage}</p>
                </div>
              </div>
            </div>
          )}

          {/* 基本设置 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">基本设置</h2>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={config.isActive}
                  onChange={(e) => setConfig(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 text-sm font-medium text-gray-700">
                  启用库存盘点功能
                </label>
              </div>
            </div>
          </div>

          {/* 权限设置 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">授权操作员</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {operators.map(operator => (
                <div key={operator.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`operator-${operator.id}`}
                    checked={config.authorizedOperators.includes(operator.id)}
                    onChange={() => handleOperatorToggle(operator.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor={`operator-${operator.id}`} className="ml-2 text-sm text-gray-700">
                    {operator.firstName} {operator.lastName} ({operator.username})
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* 产品选择设置 */}
          <div>
            <ProductGroupSelector
              selectedProducts={selectedProducts}
              onProductsChange={setSelectedProducts}
            />
          </div>

          {/* 频率设置 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">盘点频率</h2>
            <div className="space-y-4">
              <div className="flex space-x-4">
                {[
                  { value: 'daily', label: '每天' },
                  { value: 'weekly', label: '每周' },
                  { value: 'monthly', label: '每月' }
                ].map(option => (
                  <div key={option.value} className="flex items-center">
                    <input
                      type="radio"
                      id={option.value}
                      name="frequency"
                      value={option.value}
                      checked={config.frequency === option.value}
                      onChange={(e) => setConfig(prev => ({ 
                        ...prev, 
                        frequency: e.target.value as 'daily' | 'weekly' | 'monthly' 
                      }))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor={option.value} className="ml-2 text-sm text-gray-700">
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>

              <div className="flex items-center space-x-4">
                <label htmlFor="scheduleTime" className="text-sm font-medium text-gray-700">
                  盘点时间:
                </label>
                <input
                  type="time"
                  id="scheduleTime"
                  value={config.scheduleTime}
                  onChange={(e) => setConfig(prev => ({ ...prev, scheduleTime: e.target.value }))}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {config.frequency === 'weekly' && (
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">选择星期:</label>
                  <div className="flex space-x-2">
                    {['日', '一', '二', '三', '四', '五', '六'].map((day, index) => (
                      <div key={index} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`day-${index}`}
                          checked={config.scheduleDays?.includes(index) || false}
                          onChange={() => handleScheduleDayToggle(index)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`day-${index}`} className="ml-1 text-sm text-gray-700">
                          {day}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {config.frequency === 'monthly' && (
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">选择日期:</label>
                  <div className="grid grid-cols-7 gap-2">
                    {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                      <div key={day} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`date-${day}`}
                          checked={config.scheduleDays?.includes(day) || false}
                          onChange={() => handleScheduleDayToggle(day)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`date-${day}`} className="ml-1 text-xs text-gray-700">
                          {day}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end pt-6 border-t border-gray-200">
            <Button
              onClick={handleSaveConfig}
              disabled={isSaving}
              className="px-6 py-2"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <span className="mr-2">💾</span>
                  保存配置
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
