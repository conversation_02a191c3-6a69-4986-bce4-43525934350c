'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/hooks/use-auth';
import { usePermissions } from '@/hooks/use-permissions';
import { ProductList } from '@/components/products/product-list';
import { ProductFilters } from '@/components/products/product-filters';
import { AddProductModal } from '@/components/products/add-product-modal';
import { Button } from '@/components/ui/button';
import { ProductFilters as ProductFiltersType } from '@/lib/types';

export default function ProductsPage() {
  const { isAuthenticated, token, logout } = useAuth();
  const { canAccessInventoryCount } = usePermissions();
  const router = useRouter();
  const [authDebug, setAuthDebug] = useState<string>('');
  const [selectedProductGroupId, setSelectedProductGroupId] = useState<number | undefined>(undefined);
  const [filters, setFilters] = useState<ProductFiltersType>({});
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      setAuthDebug('未认证，重定向到登录页面');
      router.push('/login');
    } else {
      setAuthDebug(`已认证，Token: ${token ? '存在' : '不存在'}`);
    }
  }, [isAuthenticated, router, token]);

  const handleProductGroupSelect = (productGroupId: number | undefined) => {
    setSelectedProductGroupId(productGroupId);
    console.log('选择的产品组ID:', productGroupId);
  };

  const handleFiltersChange = (newFilters: ProductFiltersType) => {
    setFilters(newFilters);
    console.log('筛选条件变化:', newFilters);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">正在检查认证状态...</p>
          {authDebug && (
            <p className="text-sm text-blue-600 mt-2">{authDebug}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* 页面标题和添加产品按钮 */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-600 text-lg">📦</span>
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">产品库存管理</h1>
                  <div className="flex items-center space-x-2 mt-1">
                    {canAccessInventoryCount && (
                      <Link href="/inventory-count">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer transition-colors">
                          📋 库存盘点
                        </span>
                      </Link>
                    )}
                    <Link href="/stock-notifications">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 hover:bg-orange-200 cursor-pointer transition-colors">
                        🔔 库存通知
                      </span>
                    </Link>
                    <Link href="/stock-notifications/records">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer transition-colors">
                        📝 通知记录
                      </span>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant="primary"
                  onClick={() => setIsAddModalOpen(true)}
                  className="px-4 py-2"
                >
                  添加产品
                </Button>
                <Button
                  variant="outline"
                  className="px-4 py-2"
                  onClick={logout}
                >
                  登出
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 固定筛选区域 */}
        <div className="sticky top-16 z-40 bg-gray-50 border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            {/* 认证调试信息 */}
            {authDebug && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">{authDebug}</p>
              </div>
            )}

            <ProductFilters
              onProductGroupSelect={handleProductGroupSelect}
              onFiltersChange={handleFiltersChange}
            />
          </div>
        </div>

        {/* 可滚动的产品列表区域 */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <ProductList
              selectedProductGroupId={selectedProductGroupId}
              filters={filters}
            />
          </div>
        </main>

        {/* 添加产品模态框 */}
        <AddProductModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onSuccess={() => {
            // 刷新产品列表 - 这里可以通过状态管理或回调来实现
            window.location.reload();
          }}
        />
    </div>
  );
}