import { ApiService } from './api';

interface StockCacheEntry {
  stock: number | string;
  timestamp: number;
}

class StockCache {
  private cache = new Map<number, StockCacheEntry>();
  private readonly CACHE_DURATION = 30000; // 30秒缓存
  private pendingRequests = new Map<number, Promise<number | string>>();

  async getStock(productId: number): Promise<number | string> {
    // 检查缓存
    const cached = this.cache.get(productId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.stock;
    }

    // 检查是否有正在进行的请求
    const pendingRequest = this.pendingRequests.get(productId);
    if (pendingRequest) {
      return pendingRequest;
    }

    // 创建新的请求
    const request = this.fetchStockFromAPI(productId);
    this.pendingRequests.set(productId, request);

    try {
      const stock = await request;
      // 缓存结果
      this.cache.set(productId, {
        stock,
        timestamp: Date.now()
      });
      return stock;
    } finally {
      // 清除待处理的请求
      this.pendingRequests.delete(productId);
    }
  }

  private async fetchStockFromAPI(productId: number): Promise<number | string> {
    try {
      const stockInfo = await ApiService.fetchProductStock(productId);
      if (typeof stockInfo === 'string') {
        return stockInfo;
      } else if (stockInfo && typeof stockInfo.onHand === 'number') {
        return stockInfo.onHand;
      } else {
        return '未知';
      }
    } catch {
      return '错误';
    }
  }

  // 批量获取库存（限制并发数）
  async getBatchStock(productIds: number[], maxConcurrent = 5): Promise<Map<number, number | string>> {
    const results = new Map<number, number | string>();
    
    // 分批处理，避免过多并发请求
    for (let i = 0; i < productIds.length; i += maxConcurrent) {
      const batch = productIds.slice(i, i + maxConcurrent);
      const batchPromises = batch.map(async (productId) => {
        const stock = await this.getStock(productId);
        return { productId, stock };
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ productId, stock }) => {
        results.set(productId, stock);
      });
    }

    return results;
  }

  // 清除缓存
  clearCache(): void {
    this.cache.clear();
  }

  // 清除特定产品的缓存
  clearProductCache(productId: number): void {
    this.cache.delete(productId);
  }

  // 预加载库存数据
  async preloadStock(productIds: number[]): Promise<void> {
    await this.getBatchStock(productIds, 3); // 限制并发数为3
  }

  // 同步获取缓存的库存数据（只返回已缓存的数据）
  getCachedStock(productId: number): number | string | null {
    const cached = this.cache.get(productId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.stock;
    }
    return null;
  }
}

// 导出单例实例
export const stockCache = new StockCache();
