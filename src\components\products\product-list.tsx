'use client';
import { ProductCard } from './product-card';
import { LoadingIndicator } from '@/components/ui/loading';
import { useProductSearch } from '@/hooks/use-product-search';
import { type ProductFilters } from '@/lib/types';

interface ProductListProps {
  selectedProductGroupId?: number | undefined;
  filters?: ProductFilters;
}

export function ProductList({ selectedProductGroupId, filters: externalFilters }: ProductListProps) {
  const {
    products,
    isLoading,
    error,
    totalCount,
    refresh,
  } = useProductSearch(externalFilters || {}, selectedProductGroupId, {
    debounceMs: 500,
  });


  // 如果没有选择产品组且没有搜索条件，显示提示信息
  if (!selectedProductGroupId && !externalFilters?.search) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">📦</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">请选择产品组或输入搜索条件</h3>
        <p className="text-gray-500">请在上方选择产品组以查看相关产品，或输入搜索条件进行全局搜索</p>
      </div>
    );
  }

  // 初始加载状态
  if (isLoading && products.length === 0) {
    return (
      <div className="flex justify-center py-12">
        <LoadingIndicator text="正在搜索产品..." />
      </div>
    );
  }

  // 错误状态
  if (error && products.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">搜索失败: {error}</p>
        <button
          onClick={refresh}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 搜索结果统计 */}
      {(externalFilters?.search || selectedProductGroupId) && (
        <div className="text-sm text-gray-600 px-1">
          找到 {totalCount} 个产品，已显示 {products.length} 个
        </div>
      )}

      {products.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📦</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到产品</h3>
          <p className="text-gray-500">请尝试调整搜索条件</p>
        </div>
      ) : (
        <div className="space-y-4">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onUpdate={refresh}
            />
          ))}
        </div>
      )}
    </div>
  );
}