import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(price);
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function formatStock(stock: number | string): string {
  if (typeof stock === 'string') {
    return stock;
  }
  
  if (stock === 0) {
    return '0';
  }
  
  return stock.toString();
}

export function getStockColor(stock: number | string): string {
  if (typeof stock === 'string') {
    return 'text-gray-600';
  }
  
  if (stock === 0) {
    return 'text-red-600';
  }
  
  if (stock < 10) {
    return 'text-orange-600';
  }
  
  return 'text-green-600';
} 