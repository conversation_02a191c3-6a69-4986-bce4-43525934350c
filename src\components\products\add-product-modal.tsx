'use client';

import { useState, useEffect } from 'react';
import { ProductGroup, UserGroup } from '@/lib/types';
import { ApiService } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface GeneralFormData {
  productType: number;
  productGroupId: number;
  name: string;
  description: string;
  price: number;
  cost: number;
  disallowClientOrder: boolean;
  restrictGuestSale: boolean;
  restrictSale: boolean;
  purchaseOptions: number;
  points: number;
  pointsPrice: number;
  barcode: string;
  enableStock: boolean;
  disallowSaleIfOutOfStock: boolean;
  stockAlert: boolean;
  stockAlertThreshold: number;
  stockTargetDifferentProduct: boolean;
  stockTargetProductId: number;
  stockProductAmount: number;
  isDeleted: boolean;
  isService: boolean;
  displayOrder: number;
}

interface UserPriceData {
  userGroupId: number;
  price: number;
  pointsPrice: number;
  purchaseOptions: number;
  isEnabled: boolean;
  isPointsPriceManuallyModified?: boolean; // Track if pointsPrice was manually modified
}

interface RestrictionData {
  userGroupId: number;
  isDisallowed: boolean;
}

export function AddProductModal({ isOpen, onClose, onSuccess }: AddProductModalProps) {
  const [activeTab, setActiveTab] = useState('general');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);

  // General form data - 初始化为默认值
  const [generalData, setGeneralData] = useState<GeneralFormData>({
    productType: 0,
    productGroupId: 0,
    name: '',
    description: '',
    price: 0,
    cost: 0,
    disallowClientOrder: false,
    restrictGuestSale: false,
    restrictSale: false,
    purchaseOptions: 0,
    points: 0,
    pointsPrice: 0,
    barcode: '',
    enableStock: true,
    disallowSaleIfOutOfStock: true,
    stockAlert: true,
    stockAlertThreshold: 0,
    stockTargetDifferentProduct: false,
    stockTargetProductId: 0,
    stockProductAmount: 0,
    isDeleted: false,
    isService: false,
    displayOrder: 0,
  });

  // User prices data
  const [userPrices, setUserPrices] = useState<UserPriceData[]>([]);
  
  // Restrictions data
  const [restrictions, setRestrictions] = useState<RestrictionData[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadInitialData();
      resetForm();
    }
  }, [isOpen]);

  const resetForm = () => {
    setGeneralData({
      productType: 0,
      productGroupId: 0,
      name: '',
      description: '',
      price: 0,
      cost: 0,
      disallowClientOrder: false,
      restrictGuestSale: false,
      restrictSale: false,
      purchaseOptions: 0,
      points: 0,
      pointsPrice: 0,
      barcode: '',
      enableStock: true,
      disallowSaleIfOutOfStock: true,
      stockAlert: true,
      stockAlertThreshold: 0,
      stockTargetDifferentProduct: false,
      stockTargetProductId: 0,
      stockProductAmount: 0,
      isDeleted: false,
      isService: false,
      displayOrder: 0,
    });
    setActiveTab('general');
    setError(null);
  };

  const loadInitialData = async () => {
    try {
      // Load product groups
      const groupsResponse = await ApiService.fetchProductGroups();
      console.log('Product groups response:', groupsResponse);

      // 处理可能的嵌套数据结构
      let productGroupsData = [];
      if (groupsResponse.result) {
        if (Array.isArray(groupsResponse.result)) {
          productGroupsData = groupsResponse.result;
        } else if (typeof groupsResponse.result === 'object' && groupsResponse.result !== null) {
          const result = groupsResponse.result as any;
          if (result.data && Array.isArray(result.data)) {
            productGroupsData = result.data;
          } else if (result.items && Array.isArray(result.items)) {
            productGroupsData = result.items;
          }
        }
      }
      setProductGroups(productGroupsData);

      // Load user groups
      const userGroupsResponse = await ApiService.fetchUserGroups();
      console.log('User groups response:', userGroupsResponse);

      // 处理可能的嵌套数据结构
      let userGroupsData = [];
      if (userGroupsResponse.result) {
        if (Array.isArray(userGroupsResponse.result)) {
          userGroupsData = userGroupsResponse.result;
        } else if (userGroupsResponse.result.data && Array.isArray(userGroupsResponse.result.data)) {
          userGroupsData = userGroupsResponse.result.data;
        } else if (userGroupsResponse.result.items && Array.isArray(userGroupsResponse.result.items)) {
          userGroupsData = userGroupsResponse.result.items;
        }
      }

      if (userGroupsData.length > 0) {
        setUserGroups(userGroupsData);

        // Initialize user prices for all user groups
        const initialUserPrices = userGroupsData.map((group: UserGroup) => ({
          userGroupId: group.id,
          price: 0, // Default price
          pointsPrice: 0,
          purchaseOptions: 0,
          isEnabled: false, // Default to disabled for new products
          isPointsPriceManuallyModified: false, // Initially not manually modified
        }));
        setUserPrices(initialUserPrices);

        // Initialize restrictions for all user groups
        const initialRestrictions = userGroupsData.map((group: UserGroup) => ({
          userGroupId: group.id,
          isDisallowed: false,
        }));
        setRestrictions(initialRestrictions);
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      setError('加载数据失败');
    }
  };

  const handleGeneralChange = (field: keyof GeneralFormData, value: any) => {
    setGeneralData(prev => ({ ...prev, [field]: value }));

    // When general price changes, update all enabled user prices to match
    if (field === 'price') {
      setUserPrices(prev => prev.map(up =>
        up.isEnabled ? { ...up, price: value } : { ...up, price: value }
      ));
    }

    // When general pointsPrice changes, update pointsPrice for user groups that haven't been manually modified
    if (field === 'pointsPrice') {
      setUserPrices(prev => prev.map(up =>
        !up.isPointsPriceManuallyModified ? { ...up, pointsPrice: value } : up
      ));
    }



    // When general purchaseOptions changes, update all enabled user prices to match
    if (field === 'purchaseOptions') {
      setUserPrices(prev => prev.map(up =>
        up.isEnabled ? { ...up, purchaseOptions: value } : { ...up, purchaseOptions: value }
      ));
    }
  };

  const handleUserPriceChange = (userGroupId: number, field: keyof UserPriceData, value: any) => {
    setUserPrices(prev => prev.map(up => {
      if (up.userGroupId === userGroupId) {
        const updatedPrice = { ...up, [field]: value };
        // Mark pointsPrice as manually modified when user changes it
        if (field === 'pointsPrice') {
          updatedPrice.isPointsPriceManuallyModified = true;
        }
        return updatedPrice;
      }
      return up;
    }));
  };

  const handleRestrictionChange = (userGroupId: number, isDisallowed: boolean) => {
    setRestrictions(prev => prev.map(r => 
      r.userGroupId === userGroupId ? { ...r, isDisallowed } : r
    ));
  };

  const handleSubmit = async () => {
    if (!generalData.name.trim()) {
      setError('产品名称不能为空');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 1. Create new product with cleaned data
      const cleanedData = {
        ...generalData,
        productGroupId: generalData.productGroupId || undefined, // Don't send 0
        stockTargetProductId: generalData.stockTargetProductId || undefined, // Don't send 0
      };
      
      console.log('Sending product data:', cleanedData);
      const productResponse = await ApiService.createProductComplete(cleanedData);
      
      if (!productResponse.result?.id) {
        throw new Error('创建产品失败：未返回产品ID');
      }

      const newProductId = productResponse.result.id;

      // 2. Create user prices for enabled groups
      for (const userPrice of userPrices) {
        if (userPrice.isEnabled) {
          await ApiService.createProductUserPrice(newProductId, {
            userGroupId: userPrice.userGroupId,
            price: userPrice.price,
            pointsPrice: userPrice.pointsPrice,
            purchaseOptions: userPrice.purchaseOptions,
            isEnabled: userPrice.isEnabled,
          });
        }
      }

      // 3. Create restrictions for disallowed groups
      for (const restriction of restrictions) {
        if (restriction.isDisallowed) {
          console.log('创建访问限制:', { productId: newProductId, userGroupId: restriction.userGroupId });
          await ApiService.createDisallowedUserGroup(newProductId, {
            productId: newProductId,
            userGroupId: restriction.userGroupId,
            isDisallowed: restriction.isDisallowed,
          });
        }
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error('产品创建失败:', err);
      let errorMessage = '创建产品失败';
      
      if (err instanceof Error) {
        // 检查是否是重复名称错误
        if (err.message.includes('is not unique') || err.message.includes('NonUniqueEntityValue')) {
          errorMessage = `产品名称 "${generalData.name}" 已存在，请使用不同的名称`;
        }
        // 检查其他特定错误
        else if (err.message.includes('400')) {
          errorMessage = '请检查输入数据是否有效: ' + err.message;
        }
        // 检查服务器错误
        else if (err.message.includes('500')) {
          errorMessage = '服务器内部错误，请稍后重试';
        }
        else {
          errorMessage = err.message;
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>添加新产品</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general" className="text-sm font-medium">通用设置</TabsTrigger>
            <TabsTrigger value="pricing" className="text-sm font-medium">用户组价格</TabsTrigger>
            <TabsTrigger value="restrictions" className="text-sm font-medium">访问限制</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">产品名称 *</label>
                <Input
                  value={generalData.name}
                  onChange={(e) => handleGeneralChange('name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">产品组</label>
                <select
                  value={generalData.productGroupId.toString()}
                  onChange={(e) => handleGeneralChange('productGroupId', parseInt(e.target.value))}
                  className="flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm text-black ring-offset-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="0">请选择产品组</option>
                  {productGroups.map(group => (
                    <option key={group.id} value={group.id}>{group.name}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">产品描述</label>
              <Textarea
                value={generalData.description}
                onChange={(e) => handleGeneralChange('description', e.target.value)}
                rows={3}
              />
            </div>

            {/* 价格和成本 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">价格 (RM)</label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">RM</span>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={generalData.price}
                    onChange={(e) => handleGeneralChange('price', parseFloat(e.target.value) || 0)}
                    className="text-base font-medium"
                  />
                  <div className="flex items-center space-x-2">
                    <select
                      value={generalData.purchaseOptions}
                      onChange={(e) => handleGeneralChange('purchaseOptions', parseInt(e.target.value))}
                      className="px-2 py-1 border border-gray-300 rounded text-sm bg-blue-500 text-white"
                    >
                      <option value={1}>Or</option>
                      <option value={0}>And</option>
                    </select>
                    <Input
                      type="number"
                      min="0"
                      value={generalData.pointsPrice}
                      onChange={(e) => handleGeneralChange('pointsPrice', parseInt(e.target.value) || 0)}
                      className="w-20 text-base font-medium"
                      placeholder="积分"
                    />
                    <span className="text-sm text-gray-600">Points</span>
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">成本 (RM)</label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">RM</span>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={generalData.cost}
                    onChange={(e) => handleGeneralChange('cost', parseFloat(e.target.value) || 0)}
                    className="text-base font-medium"
                  />
                </div>
              </div>
            </div>

            {/* 奖励积分和条码 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">奖励积分</label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    min="0"
                    value={generalData.points}
                    onChange={(e) => handleGeneralChange('points', parseInt(e.target.value) || 0)}
                    className="text-base font-medium"
                  />
                  <span className="text-sm text-gray-600">Points</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-2">条码</label>
                <Input
                  value={generalData.barcode}
                  onChange={(e) => handleGeneralChange('barcode', e.target.value)}
                  className="text-base font-medium"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.enableStock}
                    onChange={(e) => handleGeneralChange('enableStock', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">启用库存管理</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.disallowSaleIfOutOfStock}
                    onChange={(e) => handleGeneralChange('disallowSaleIfOutOfStock', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">缺货时禁止销售</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.stockAlert}
                    onChange={(e) => handleGeneralChange('stockAlert', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">库存警报</label>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.restrictSale}
                    onChange={(e) => handleGeneralChange('restrictSale', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">限制销售</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.restrictGuestSale}
                    onChange={(e) => handleGeneralChange('restrictGuestSale', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">限制访客购买</label>
                </div>
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={generalData.isService}
                    onChange={(e) => handleGeneralChange('isService', e.target.checked)}
                  />
                  <label className="text-sm font-medium text-gray-900">服务类产品</label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">用户组价格设置</h3>
              <p className="text-sm text-gray-700 font-medium">
                勾选ENABLE来启用特定用户组的价格设置。启用后价格会自动与通用价格同步。
              </p>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-blue-100">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">ENABLE</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">USER GROUP</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">PRICE</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">OPTIONS</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">POINTS PRICE</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userGroups.map(group => {
                      const userPrice = userPrices.find(up => up.userGroupId === group.id);
                      if (!userPrice) return null;

                      return (
                        <tr key={group.id} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-3 py-2">
                            <Checkbox
                              checked={userPrice.isEnabled}
                              onChange={(e) => handleUserPriceChange(group.id, 'isEnabled', e.target.checked)}
                            />
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-sm font-semibold text-gray-900">
                            {group.name}
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <div className="flex items-center space-x-1">
                              <span className="text-xs text-gray-600">RM</span>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                value={userPrice.price}
                                onChange={(e) => handleUserPriceChange(group.id, 'price', parseFloat(e.target.value) || 0)}
                                className="w-20 text-sm font-medium"
                                disabled={!userPrice.isEnabled}
                              />
                            </div>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <select
                              value={userPrice.purchaseOptions}
                              onChange={(e) => handleUserPriceChange(group.id, 'purchaseOptions', parseInt(e.target.value))}
                              className="px-2 py-1 border border-gray-300 rounded text-xs bg-blue-500 text-white font-medium"
                              disabled={!userPrice.isEnabled}
                            >
                              <option value={1}>Or</option>
                              <option value={0}>And</option>
                            </select>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Input
                              type="number"
                              min="0"
                              value={userPrice.pointsPrice}
                              onChange={(e) => handleUserPriceChange(group.id, 'pointsPrice', parseInt(e.target.value) || 0)}
                              className="w-20 text-sm font-medium"
                              disabled={!userPrice.isEnabled}
                            />
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="restrictions" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">用户组访问限制</h3>
              <p className="text-sm text-gray-700 font-medium">
                勾选DISALLOWED来禁止特定用户组访问此产品。
              </p>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-blue-100">
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">NAME</th>
                      <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold text-gray-900">DISALLOWED</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userGroups.map(group => {
                      const restriction = restrictions.find(r => r.userGroupId === group.id);
                      if (!restriction) return null;

                      return (
                        <tr key={group.id} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-3 py-2 text-sm font-semibold text-gray-900">
                            {group.name}
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            <Checkbox
                              checked={restriction.isDisallowed}
                              onChange={(e) => handleRestrictionChange(group.id, e.target.checked)}
                            />
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? '创建中...' : '创建产品'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
