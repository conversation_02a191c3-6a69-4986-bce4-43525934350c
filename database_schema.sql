-- 库存盘点系统数据库表结构

-- 1. 管理员权限控制设置表
CREATE TABLE inventory_count_configs (
    id SERIAL PRIMARY KEY,
    config_name VARCHAR(255) NOT NULL DEFAULT 'Default Config',
    authorized_operators INTEGER[] NOT NULL DEFAULT '{}',
    frequency VARCHAR(20) NOT NULL DEFAULT 'daily', -- daily, weekly, monthly
    schedule_time TIME NOT NULL DEFAULT '09:00:00',
    schedule_days INTEGER[] DEFAULT '{}', -- 0-6 for weekly, 1-31 for monthly
    is_active BOOLEAN NOT NULL DEFAULT false,
    created_by INTEGER NOT NULL, -- 创建者操作员ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 盘点配置产品表（存储具体选中的产品）
CREATE TABLE inventory_count_config_products (
    id SERIAL PRIMARY KEY,
    config_id INTEGER NOT NULL REFERENCES inventory_count_configs(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_group_id INTEGER,
    product_group_name VARCHAR(255),
    product_barcode VARCHAR(255),
    is_selected BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. 库存盘点会话表
CREATE TABLE inventory_count_sessions (
    id VARCHAR(50) PRIMARY KEY, -- 格式: INV-YYYYMMDD-HHMMSS-{operator_id}
    config_id INTEGER REFERENCES inventory_count_configs(id),
    operator_id INTEGER NOT NULL,
    operator_name VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'in_progress', -- in_progress, completed, cancelled
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    discrepancy_items INTEGER DEFAULT 0,
    total_discrepancy_value DECIMAL(10,2) DEFAULT 0, -- 总差异金额
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 4. 库存盘点记录表（核心表）
CREATE TABLE inventory_count_records (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL REFERENCES inventory_count_sessions(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_barcode VARCHAR(255),
    product_group_id INTEGER,
    product_group_name VARCHAR(255),
    
    -- 库存信息
    system_stock INTEGER NOT NULL, -- 系统库存（从API获取）
    counted_stock INTEGER, -- 实际盘点数量（操作员输入）
    difference INTEGER DEFAULT 0, -- 差异数量 (system_stock - counted_stock)
    
    -- 价值信息（可选）
    unit_price DECIMAL(10,2) DEFAULT 0,
    difference_value DECIMAL(10,2) DEFAULT 0, -- 差异金额
    
    -- 操作信息
    operator_id INTEGER NOT NULL,
    operator_name VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, counted, submitted
    count_date DATE DEFAULT CURRENT_DATE,
    count_time TIME DEFAULT CURRENT_TIME,
    
    -- 备注和审计
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. 库存盘点历史汇总表（用于快速查询和报告）
CREATE TABLE inventory_count_summaries (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL REFERENCES inventory_count_sessions(id),
    summary_date DATE NOT NULL,
    operator_id INTEGER NOT NULL,
    operator_name VARCHAR(255) NOT NULL,
    
    total_products INTEGER NOT NULL,
    counted_products INTEGER NOT NULL,
    products_with_discrepancy INTEGER NOT NULL,
    
    total_system_stock INTEGER NOT NULL,
    total_counted_stock INTEGER NOT NULL,
    total_difference INTEGER NOT NULL,
    total_difference_value DECIMAL(10,2) DEFAULT 0,
    
    accuracy_percentage DECIMAL(5,2) DEFAULT 0, -- 准确率百分比
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX idx_inventory_config_products_config_id ON inventory_count_config_products(config_id);
CREATE INDEX idx_inventory_config_products_product_id ON inventory_count_config_products(product_id);
CREATE INDEX idx_inventory_config_products_group_id ON inventory_count_config_products(product_group_id);

CREATE INDEX idx_inventory_sessions_operator ON inventory_count_sessions(operator_id);
CREATE INDEX idx_inventory_sessions_status ON inventory_count_sessions(status);
CREATE INDEX idx_inventory_sessions_date ON inventory_count_sessions(start_time);

CREATE INDEX idx_inventory_records_session ON inventory_count_records(session_id);
CREATE INDEX idx_inventory_records_product ON inventory_count_records(product_id);
CREATE INDEX idx_inventory_records_operator ON inventory_count_records(operator_id);
CREATE INDEX idx_inventory_records_date ON inventory_count_records(count_date);
CREATE INDEX idx_inventory_records_status ON inventory_count_records(status);
CREATE INDEX idx_inventory_records_difference ON inventory_count_records(difference);

CREATE INDEX idx_inventory_summaries_date ON inventory_count_summaries(summary_date);
CREATE INDEX idx_inventory_summaries_operator ON inventory_count_summaries(operator_id);

-- 创建触发器自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_inventory_configs_updated_at BEFORE UPDATE ON inventory_count_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_sessions_updated_at BEFORE UPDATE ON inventory_count_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_records_updated_at BEFORE UPDATE ON inventory_count_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认配置
INSERT INTO inventory_count_configs (
    config_name, 
    authorized_operators, 
    frequency, 
    schedule_time, 
    is_active, 
    created_by
) VALUES (
    'Default Inventory Count Config',
    '{2237}',
    'daily',
    '09:00:00',
    true,
    2237
);

-- 创建视图用于快速查询盘点统计
CREATE VIEW inventory_count_stats AS
SELECT 
    s.id as session_id,
    s.operator_name,
    s.start_time,
    s.status,
    COUNT(r.id) as total_items,
    COUNT(CASE WHEN r.counted_stock IS NOT NULL THEN 1 END) as completed_items,
    COUNT(CASE WHEN r.difference != 0 THEN 1 END) as discrepancy_items,
    SUM(r.difference) as total_difference,
    SUM(r.difference_value) as total_difference_value,
    ROUND(
        (COUNT(CASE WHEN r.difference = 0 THEN 1 END)::DECIMAL / NULLIF(COUNT(CASE WHEN r.counted_stock IS NOT NULL THEN 1 END), 0)) * 100, 
        2
    ) as accuracy_percentage
FROM inventory_count_sessions s
LEFT JOIN inventory_count_records r ON s.id = r.session_id
GROUP BY s.id, s.operator_name, s.start_time, s.status;

COMMENT ON TABLE inventory_count_configs IS '库存盘点配置表 - 存储盘点权限和频率设置';
COMMENT ON TABLE inventory_count_config_products IS '盘点配置产品表 - 存储管理员选择的具体产品';
COMMENT ON TABLE inventory_count_sessions IS '库存盘点会话表 - 存储每次盘点会话信息';
COMMENT ON TABLE inventory_count_records IS '库存盘点记录表 - 存储详细的盘点数据';
COMMENT ON TABLE inventory_count_summaries IS '库存盘点汇总表 - 存储盘点统计数据';
COMMENT ON VIEW inventory_count_stats IS '库存盘点统计视图 - 用于快速查询盘点统计信息';
