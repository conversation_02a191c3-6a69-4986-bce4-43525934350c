// 速率限制器类
class RateLimiter {
  private requests: number[] = [];
  private maxRequestsPerSecond: number;
  private requestQueue: Array<{
    resolve: (value: any) => void;
    reject: (reason: any) => void;
    fn: () => Promise<any>;
  }> = [];
  private isProcessing = false;

  constructor(maxRequestsPerSecond: number = 4) { // 设置为4，略低于限制的5
    this.maxRequestsPerSecond = maxRequestsPerSecond;
  }

  // 检查是否可以发送请求
  private canMakeRequest(): boolean {
    const now = Date.now();
    // 清理1秒前的请求记录
    this.requests = this.requests.filter(time => now - time < 1000);
    
    return this.requests.length < this.maxRequestsPerSecond;
  }

  // 记录请求时间
  private recordRequest(): void {
    this.requests.push(Date.now());
  }

  // 等待直到可以发送请求
  private async waitForSlot(): Promise<void> {
    return new Promise((resolve) => {
      const checkSlot = () => {
        if (this.canMakeRequest()) {
          this.recordRequest();
          resolve(undefined);
        } else {
          // 计算需要等待的时间
          const oldestRequest = Math.min(...this.requests);
          const waitTime = 1000 - (Date.now() - oldestRequest) + 50; // 额外50ms缓冲
          setTimeout(checkSlot, Math.max(waitTime, 100));
        }
      };
      checkSlot();
    });
  }

  // 执行被速率限制的请求
  async execute<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        resolve,
        reject,
        fn: requestFn
      });
      
      this.processQueue();
    });
  }

  // 处理请求队列
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.requestQueue.length > 0) {
      const { resolve, reject, fn } = this.requestQueue.shift()!;
      
      try {
        await this.waitForSlot();
        const result = await fn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    }

    this.isProcessing = false;
  }

  // 获取队列状态
  getQueueStatus(): { queueLength: number; recentRequests: number } {
    const now = Date.now();
    const recentRequests = this.requests.filter(time => now - time < 1000).length;
    
    return {
      queueLength: this.requestQueue.length,
      recentRequests
    };
  }
}

// 创建BoxHero专用的速率限制器实例
export const boxHeroRateLimiter = new RateLimiter(4); // 每秒最多4个请求